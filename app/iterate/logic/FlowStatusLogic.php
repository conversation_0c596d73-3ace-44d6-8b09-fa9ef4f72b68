<?php
/**
 * Desc 工作流 - 逻辑层
 * User Long
 * Date 2024/07/23
 */
declare (strict_types=1);

namespace app\iterate\logic;

use app\infrastructure\model\EnumModel;
use app\iterate\model\FlowStatusModel;
use app\iterate\model\FlowStatusTextModel;
use app\iterate\validate\FlowStatusValidate;
use app\project\logic\ProjectCategorySettingsLogic;
use app\project\logic\ProjectInfoLogic;
use app\project\logic\ProjectTemplateLogic;
use app\project\model\ProjectModel;
use app\project\model\ProjectTemplateModel;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseLogic;
use basic\BaseModel;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\BusinessException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;
use Throwable;

class FlowStatusLogic extends BaseLogic
{
    private const RENAME_URL = '/project/flowStatus/rename'; // 重命名链接
    private int $statusMaxNum = 10; // 工作流数量最大限制：单项目最多可添加10个状态
    private int $flowStatusCollectionMaxNum = 20; // 工作流状态数量最大限制：单工作流最多可添加20个状态
    private int $flowStatusType; // 工作流类型 1=>迭代 2=>需求 3=>任务 4=>缺陷
    private string $flowStatusValidateName = ''; // 验证器模块名 1=>Iteration 2=>Demand

    public function __construct(int $flowStatusType = BaseModel::SETTING_TYPE_ITERATION)
    {
        // 工作库仅支持 迭代、需求、任务、缺陷
        if ($flowStatusType > BaseModel::SETTING_TYPE_DEFECT) {
            throw new ParamsException('所属类型不在预设访问内，请联系客服！');
        }

        $this->flowStatusType = $flowStatusType;

        // 根据工作流类型，设置状态库类型
        switch ($this->flowStatusType) {
        case BaseModel::SETTING_TYPE_ITERATION:
            $this->flowStatusValidateName = 'Iteration';
            break;
        case BaseModel::SETTING_TYPE_DEMAND:
        case BaseModel::SETTING_TYPE_DEFECT:
            $this->flowStatusValidateName = 'WorkItem';
            break;
        default:
            break;
        }
    }

    /**
     * 校验状态类型
     * @param  array  $startStatusType
     * @param  array  $endStatusType
     * User Long
     * Date 2024/8/9
     */
    private function validateStatusType(array $startStatusType, array $endStatusType): void
    {
        if ( ! $startStatusType && ! $endStatusType) {
            throw new ParamsException('请添加“开始/结束状态“类型');
        }

        if ( ! $endStatusType) {
            throw new ParamsException('请添加“结束状态”类型');
        }

        if ( ! $startStatusType) {
            throw new ParamsException('请添加“开始状态”类型');
        }

        if (count($startStatusType) > 1) {
            throw new ParamsException('“开始状态”只可选择一个');
        }
    }

    /**
     * 校验状态库数据是否异常
     * @param  array  $statusEnumIds
     * @throws DbException
     * User Long
     * Date 2024/8/10
     */
    private function validateStatusEnumNum(array $statusEnumIds): void
    {
        // 校验状态是否存在重复
        $statusEnumNum = count($statusEnumIds);
        if ($statusEnumNum != count(array_unique($statusEnumIds))) {
            throw new ParamsException('存在重复选择的状态，请检查后重新保存');
        }

        // 校验状态是否存在删除
        if ($statusEnumNum != FlowStatusEnumLogic::countByEnumIds($this->flowStatusType, $statusEnumIds)) {
            throw new ParamsException('存在已删除的状态，请刷新页面');
        }
    }

    /**
     * 校验节点与工作流程是否一致
     * @param  int    $flowProcessId
     * @param  array  $processNodeIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    private function validateProcessNode(int $flowProcessId, array $processNodeIds): void
    {
        if ( ! $processNodeIds) {
            return;
        }

        $processNodeInfo = FlowProcessLogic::selectProcessNodeByIds($processNodeIds);

        if ( ! $processNodeInfo->where('flow_process_id', '<>', $flowProcessId)->isEmpty()) {
            throw new ParamsException('工作流程与节点存在不一致，请刷新后重新添加');
        }
    }

    /**
     * 校验流转状态是否存在列表中
     * @param  array  $statusEnumIds
     * @param  array  $targetStatusEnumIds
     * User Long
     * Date 2024/8/26
     */
    private function validateStatusEnumTransfer(array $statusEnumIds, array $targetStatusEnumIds): void
    {
        // 流转状态为空 直接跳出
        if ( ! $targetStatusEnumIds) {
            return;
        }

        $difference = array_diff($targetStatusEnumIds, $statusEnumIds);
        if ($difference) {
            throw new ParamsException('存在未知的流转状态，请仔细检查后保存');
        }
    }

    /**
     * 校验名称是否与模板相同
     * @param          $projectInfo
     * @param  string  $statusFlowName
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function validateProjectFlowStatusData($projectInfo, string $statusFlowName = ''): void
    {
        if ($projectInfo) {
            if ($projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateId = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);

                if ($templateId) {
                    // 检查工作流名称是否已存在
                    $statusFlowNameExist = FlowStatusModel::findStatusFlowAndName($templateId, $this->flowStatusType, $statusFlowName);

                    if ($statusFlowNameExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id).'-工作流名称不可相同');
                    }
                }
            } else {
                $projectIds = ProjectInfoLogic::getProjectIdsByTemplateId($projectInfo->project_template_id)
                    ->where('is_template', ProjectTemplateModel::IS_TEMPLATE_NO);
                $flowStatusNameExist = FlowStatusModel::status()
                    ->with([
                        'project' => function ($sql) {
                            $sql->bind(['project_name']);
                        }
                    ])
                    ->whereIn('project_id', $projectIds->column('project_id'))
                    ->select();

                if ( ! $flowStatusNameExist->where('status_flow_name', $statusFlowName)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $flowStatusNameExist->where('status_flow_name', $statusFlowName)->column('project_name')).'】-工作流程名称已存在');
                }
            }
        }
    }

    /**
     * 处理状态数据集合
     * @param  array  $flowStatusCollection
     * @return array [
     * 'processNodeIds' => 工作流程节点id集,
     * 'statusEnumIds' => 状态库id集,
     * 'startStatusType' => 开始状态组,
     * 'endStatusType' => 结束状态组
     * 'targetStatusEnumIds' => 流转的状态库id集
     * ]
     * User Long
     * Date 2024/8/26
     */
    private function handleFlowStatusCollection(array $flowStatusCollection): array
    {
        if (count($flowStatusCollection) > $this->flowStatusCollectionMaxNum) {
            throw new ParamsException('仅限添加'.$this->flowStatusCollectionMaxNum.'个工作流状态');
        }

        $processNodeIds = $statusEnumIds = $startStatusType = $endStatusType = $targetStatusEnumIds = [];
        foreach ($flowStatusCollection as $param) {
            // 校验 - 工作流状态设置
            validate(FlowStatusValidate::class)->scene('flowStatusCollection')->check($param);

            // 当前类别所属为迭代，并且 工作流程节点id集 不为空才执行
            if ($this->flowStatusType == BaseModel::SETTING_TYPE_ITERATION && ! empty($param['process_node_ids'])) {
                // 获取节点id
                $processNodeIds = array_merge($processNodeIds, $param['process_node_ids']);
            }

            // 当前类别所属为需求、缺陷，并且 状态流转数据 不为空才执行
            if (in_array($this->flowStatusType, [BaseModel::SETTING_TYPE_DEMAND, BaseModel::SETTING_TYPE_DEFECT]) && ! empty($param['flow_status_transfer'])) {
                foreach ($param['flow_status_transfer'] as $flowStatusTransfer) {
                    validate(FlowStatusValidate::class)->scene('flowStatusTransfer')->check($flowStatusTransfer);

                    // 获取转移的状态id合集
                    $targetStatusEnumIds[] = $flowStatusTransfer['target_status_enum_id'];
                }
            }

            // 获取状态id
            $statusEnumIds[] = $param['status_enum_id'];

            // 获取状态类型
            switch ($param['status_type']) {
            case FlowStatusModel::START_STATUS_TYPE:
                $startStatusType[] = FlowStatusModel::START_STATUS_TYPE;
                break;
            case FlowStatusModel::END_STATUS_TYPE:
                $endStatusType[] = FlowStatusModel::END_STATUS_TYPE;
                break;
            default:
                break;
            }
        }

        return compact('processNodeIds', 'statusEnumIds', 'startStatusType', 'endStatusType', 'targetStatusEnumIds');
    }

    /**
     * 保存状态描述数据
     * @param  int    $flowStatusId
     * @param  array  $flowStatusCollection
     * User Long
     * Date 2024/8/10
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    private function saveFlowStatusCollectionData(int $flowStatusId, array $flowStatusCollection): void
    {
        foreach ($flowStatusCollection as $param) {
            $sort = $param['sort'] ?? 1;
            // 保存状态描述
            $statusTextId = FlowStatusTextLogic::saveFlowStatusTextData($flowStatusId, (int)$param['status_enum_id'], (int)$param['status_type'], (int)$sort);

            foreach ($param['process_node_ids'] ?? [] as $processNodeId) {
                // 保存状态与节点关系
                FlowStatusNodeRelationLogic::saveFlowStatusTextData($flowStatusId, (int)$statusTextId, (int)$processNodeId);
            }

            foreach ($param['flow_status_transfer'] ?? [] as $flowStatusTransfer) {
                // 保存流转数据
                FlowStatusTransferLogic::saveFlowStatusTransferData($flowStatusId, (int)$statusTextId, (int)$param['status_enum_id'], (int)$flowStatusTransfer['target_status_enum_id'], $flowStatusTransfer['extends']);
            }
        }
    }

    /**
     * 修改相关联需求状态数据
     * @param  array  $modifyRelevanceDemand
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * User Long
     * Date 2024/8/29
     */
    private function updateModifyRelevanceDemandData(array $modifyRelevanceDemand): void
    {
        foreach ($modifyRelevanceDemand as $param) {
            (new WorkItemsLogic)->setItemsStatusId($param['work_item_ids'], $param['target_status_enum_id']);
        }
    }

    /**
     * 新增工作流
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/9
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(FlowStatusValidate::class)->scene('create'.$this->flowStatusValidateName)->check($params);

        if (FlowStatusModel::countProjectStatus((int)$params['project_id'], $this->flowStatusType) >= $this->statusMaxNum) {
            throw new ParamsException('仅限添加'.$this->statusMaxNum.'个工作流');
        }

        // 处理状态数据集合
        $flowStatusCollectionData = $this->handleFlowStatusCollection($params['flow_status_collection']);

        // 校验状态类型
        $this->validateStatusType($flowStatusCollectionData['startStatusType'], $flowStatusCollectionData['endStatusType']);

        // 检查工作流名称是否已存在
        $statusFlowNameExist = FlowStatusModel::findStatusFlowAndName((int)$params['project_id'], $this->flowStatusType, $params['status_flow_name']);
        $projectInfo = ProjectInfoLogic::getProjectData($params['project_id']);
        if ($statusFlowNameExist) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name.'-工作流名称不可相同');
        }

        // 校验节点与工作流程是否一致
        if (isset($params['flow_process_id'])) {
            $this->validateProcessNode((int)$params['flow_process_id'], $flowStatusCollectionData['processNodeIds']);
        }

        // 校验状态库数据是否异常
        $this->validateStatusEnumNum($flowStatusCollectionData['statusEnumIds']);

        // 校验流转状态是否存在列表中
        $this->validateStatusEnumTransfer($flowStatusCollectionData['statusEnumIds'], $flowStatusCollectionData['targetStatusEnumIds']);

        // 校验名称是否与模板相同
        $this->validateProjectFlowStatusData($projectInfo, $params['status_flow_name']);

        try {
            Db::startTrans();

            // 保存 状态流程
            $flowStatusId = FlowStatusModel::saveFlowStatusData(
                (int)$params['project_id'],
                (string)$params['status_flow_name'],
                (int)($params['flow_process_id'] ?? 0),
                (string)$params['status_flow_desc'],
                $this->flowStatusType
            );

            // 保存状态描述数据
            $this->saveFlowStatusCollectionData($flowStatusId, $params['flow_status_collection']);

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据id集删除工作流
     * @param  array  $flowStatusIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/9
     */
    public function delete(array $flowStatusIds): void
    {
        $models = FlowStatusModel::selectByFlowStatusIds($this->flowStatusType, $flowStatusIds);

        try {
            Db::startTrans();

            foreach ($models as $model) {
                if (ProjectCategorySettingsLogic::findCategorySettingByFlowStatusId((int)$model->flow_status_id)) {
                    throw new BusinessException('应用中的模版不可删除');
                }

                $model->save(['is_delete' => BaseModel::DELETE_YES]);

                // 删除关联关系
                FlowStatusTextLogic::deleteFlowStatusByStatusId((int)$model->flow_status_id);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 根据id更新数据
     * @param         $flowStatusId
     * @param  array  $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/7/22
     */
    public function update($flowStatusId, array $params): void
    {
        // 验证器校验
        validate(FlowStatusValidate::class)->scene('update'.$this->flowStatusValidateName)->check($params);

        $model = FlowStatusModel::findByStatusId($this->flowStatusType, $flowStatusId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        // 处理状态数据集合
        $flowStatusCollectionData = $this->handleFlowStatusCollection($params['flow_status_collection']);

        // 校验状态类型
        $this->validateStatusType($flowStatusCollectionData['startStatusType'], $flowStatusCollectionData['endStatusType']);

        // 检查工作流名称是否已存在
        $statusFlowNameExist = FlowStatusModel::findStatusFlowAndName($model->project_id, $this->flowStatusType, $params['status_flow_name']);
        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);

        if ($statusFlowNameExist && $statusFlowNameExist->flow_status_id != $flowStatusId) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name.'工作流名称不可相同');
        }

        // 校验节点与工作流程是否一致
        $this->validateProcessNode((int)$model->flow_process_id, $flowStatusCollectionData['processNodeIds']);

        // 校验状态库数据是否异常
        $this->validateStatusEnumNum($flowStatusCollectionData['statusEnumIds']);

        // 校验流转状态是否存在列表中
        $this->validateStatusEnumTransfer($flowStatusCollectionData['statusEnumIds'], $flowStatusCollectionData['targetStatusEnumIds']);

        // 校验名称是否与模板相同
        $this->validateProjectFlowStatusData($projectInfo, $params['status_flow_name']);

        // 校验 - 相关联需求状态数据
        if (isset($params['modify_relevance_demand'])) {
            foreach ($params['modify_relevance_demand'] as $modifyRelevanceDemand) {
                validate(FlowStatusValidate::class)->scene('modifyRelevance'.$this->flowStatusValidateName)->check($modifyRelevanceDemand);
            }
        }

        try {
            Db::startTrans();

            // 删除前端移除的状态描述
            FlowStatusTextLogic::deleteNotFlowStatusByIds($flowStatusId, $flowStatusCollectionData['statusEnumIds']);

            // 迭代工作流
            if ($this->flowStatusType == BaseModel::SETTING_TYPE_ITERATION) {
                // 删除旧的状态与节点关系（真删）
                FlowStatusNodeRelationLogic::destroyFlowStatusTextData($flowStatusId);
            }
            // 需求工作流
            if (in_array($this->flowStatusType, [BaseModel::SETTING_TYPE_DEMAND, BaseModel::SETTING_TYPE_DEFECT])) {
                // 删除状态流转关系（真删）
                FlowStatusTransferLogic::destroyFlowStatusTransferData($flowStatusId);
            }

            // 保存 状态流程
            $model->status_flow_name = $params['status_flow_name'];
            $model->status_flow_desc = $params['status_flow_desc'];
            $model->save();

            // 保存状态描述数据
            $this->saveFlowStatusCollectionData((int)$flowStatusId, $params['flow_status_collection']);

            // 修改相关联需求状态数据
            if (isset($params['modify_relevance_demand'])) {
                $this->updateModifyRelevanceDemandData($params['modify_relevance_demand']);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 工作流详情
     * @param  int  $flowStatusId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException User Long
     * Date 2024/7/23
     */
    public function detail(int $flowStatusId): array
    {
        $model = FlowStatusModel::findByStatusId($this->flowStatusType, $flowStatusId);

        if ( ! $model) {
            throw new NotFoundException();
        }
        $res = $model->toDetail()->toArray();

        $res['flow_process_info'] = $res['flow_status_collection'] = [];

        // 工作流程数据
        $flowProcessInfo = FlowProcessLogic::findProcessNodeById($res['flow_process_id']);
        if ($flowProcessInfo) {
            $res['flow_process_info'] = [
                'flow_process_id'   => $flowProcessInfo->flow_process_id,
                'flow_process_name' => $flowProcessInfo->flow_process_name
            ];
        }

        // 工作流状态设置
        $flowStatusCollectionInfo = FlowStatusTextLogic::selectFlowStatusTextById($res['flow_status_id']);
        if ( ! $flowStatusCollectionInfo->isEmpty()) {
            $res['flow_status_collection'] = $flowStatusCollectionInfo->toArray();
        }

        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        $templateIds = [];
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $res['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
        // 项目内模板字段不允许编辑
        if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($model->project_id, $templateIds)) {
            $res['template_not_allowed_del'] = true;
        }

        return $res;
    }

    /**
     * 列表（无分页）
     * @param  int  $projectId
     * @return FlowStatusModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public function listQuery(int $projectId): Collection|array
    {
        $projectIds[] = $projectId;
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($projectId);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);
        return FlowStatusModel::status()
            ->with([
                'categorySetting' => function ($sql) {
                    $sql->where(['is_delete' => BaseModel::DELETE_NOT]);
                }
            ])
            ->field(FlowStatusModel::LIST_FIELDS)
//            ->where(['project_id' => $projectId, 'flow_status_type' => $this->flowStatusType])
            ->where(['flow_status_type' => $this->flowStatusType])
            ->whereIn('project_id', $projectIds)
            ->order('project_id ASC, flow_status_id DESC')
            ->hidden(['categorySetting'])
            ->select()
            ->each(function ($item) use ($projectInfo, $templateIds) {
                // true 允许删除    false  不允许删除
                $item['allow_delete'] = ! isset($item['categorySetting']) ?? true;

                $item['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
                // 项目内模板字段不允许编辑
                if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($item['project_id'], $templateIds)) {
                    $item['template_not_allowed_del'] = true;
                }
                return $item;
            });
    }

    /**
     * 获取状态类型枚举数据
     * @return array
     * User Long
     * Date 2024/8/12
     */
    public function selectorStatusType(): array
    {
        return getSystemEnumLibrary(EnumModel::SELECTOR_STATUS_TYPE);
    }

    /**
     * 获取工作流下拉数据
     * @param  int  $projectId
     * @return FlowStatusModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/22
     */
    public function selectorFlowStatus(int $projectId): Collection|array
    {
        $projectIds[] = $projectId;
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($projectId);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);

        return FlowStatusModel::status()
            ->with([
                'flowProcess' => function ($sql) {
                    $sql->bind(['flow_process_name']);
                }
            ])
            ->field(FlowStatusModel::SELECTOR_FIELDS)
//            ->where(['project_id' => $projectId, 'flow_status_type' => $this->flowStatusType])
            ->where(['flow_status_type' => $this->flowStatusType])
            ->whereIn('project_id', $projectIds)
            ->order('project_id ASC, flow_status_id DESC')
            ->select()
            ->each(function ($item) use ($projectInfo, $templateIds) {
                $item['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否

                // 项目内模板字段不允许编辑
                if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($item['project_id'], $templateIds)) {
                    $item['template_not_allowed_del'] = true;
                }
            });
    }

    /**
     * 查询单条工作流数据
     * @param  int  $flowStatusType
     * @param  int  $flowStatusId
     * @return FlowStatusModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/22
     */
    public static function findFlowStatusById(int $flowStatusType, int $flowStatusId): mixed
    {
        return FlowStatusModel::findByStatusId($flowStatusType, $flowStatusId);
    }

    /**
     * 获取相关需求id集合
     * @param  int  $flowStatusId
     * @param  int  $statusEnumId
     * @return array
     * User Long
     * Date 2024/11/15
     */
    public function getRelevanceDemand(int $flowStatusId, int $statusEnumId): array
    {
        return WorkItemsLogic::getItemByStatusId($flowStatusId, $statusEnumId, $this->flowStatusType);
    }

    /**
     * 复制工作流
     * @param  int  $flowStatusId
     * @param  int  $project_id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/8/30
     */
    public function copy(int $flowStatusId, $project_id): void
    {
        if ( ! in_array($this->flowStatusType, [BaseModel::SETTING_TYPE_DEMAND, BaseModel::SETTING_TYPE_DEFECT])) {
            $msg = BaseModel::SETTING_TYPE_TEXT[BaseModel::SETTING_TYPE_DEMAND].'/'.BaseModel::SETTING_TYPE_TEXT[BaseModel::SETTING_TYPE_DEFECT];
            throw new ParamsException('仅 '.$msg.' 工作流支持复制');
        }

        $data = $this->detail($flowStatusId);

        $params = $flowStatusCollection = [];
        $params['project_id'] = $data['project_id'];
        $params['status_flow_name'] = FlowStatusModel::generateName($data['status_flow_name'], $data['project_id']);
        $params['status_flow_desc'] = $data['status_flow_desc'];
        if ($data['flow_status_collection']) {
            foreach ($data['flow_status_collection'] as $datum) {
                $flowStatusCollection[] = [
                    'status_enum_id'       => $datum['status_enum_id'],
                    'status_type'          => $datum['status_type'],
                    'flow_status_transfer' => ! empty($datum['flow_status_transfer']) ? $this->getTransferCopyData($datum['flow_status_transfer']) : []
                ];
            }
        }
        $params['flow_status_collection'] = $flowStatusCollection;

        if ($project_id) {
            $params['project_id'] = $project_id;
        }

        $this->create($params);
    }

    /**
     * 获取需要的状态流转数据
     * @param  array  $data
     * @return array
     * User Long
     * Date 2024/8/30
     */
    private function getTransferCopyData(array $data): array
    {
        $res = [];

        foreach ($data as $datum) {
            $res[] = [
                'target_status_enum_id' => $datum['target_status_enum_id'],
                'extends'               => $datum['extends']
            ];
        }

        return $res;
    }

    /**
     * 获取可流转至的状态数据
     * @param  int  $flowStatusId
     * @param  int  $statusEnumId
     * @param  int  $cntId  工作项 id，可为空
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                      User Long
     *                      Date 2024/9/2
     */
    public function getTargetStatusEnum(int $flowStatusId, int $statusEnumId, int $cntId = 0): array
    {
        return FlowStatusTextLogic::getTargetStatusEnum($flowStatusId, $statusEnumId, $cntId);
    }

    /**
     *  获取所有已结束状态
     * @param $flowStatusType int|null 类型
     * @param $projectId      int|null 项目id
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date   2024/11/12 11:12
     */
    public static function getEndStatus($flowStatusType = null, $projectId = null)
    {
        $where = ['is_delete' => BaseModel::DELETE_NOT];
        if ($flowStatusType) {
            $where['flow_status_type'] = $flowStatusType;
        }
        if ($projectId) {
            $where['project_id'] = is_array($projectId) ? $projectId : [$projectId];
            $tempIdList = [];
            ProjectModel::status()->where(['project_id' => $projectId])->select()->each(function ($project) use (&$tempIdList) {
                if ($project->project_template_id) {
                    $tempIdList[] = $project->project_template_id;
                }
            });

            if ($tempIdList) {
                $where['project_id'] = array_merge($where['project_id'], ProjectTemplateModel::status()->where(['project_template_id' => $tempIdList])->column('project_id'));
            }
            $where['project_id'] = array_filter($where['project_id']);
        }

        $result = FlowStatusModel::status()->with([
            'statusText' => function (Query $query) {
                $query->where([
                    ['is_delete', '=', BaseModel::DELETE_NOT],
                    ['status_type', '=', FlowStatusTextModel::END_STATUS_TYPE],
                ]);
            }
        ])->where($where)->select()->toArray();

        return array_merge(...array_column($result, 'statusText'));

    }

    /**
     * 复制工作流
     * @param  int    $oldProjectId
     * @param  int    $newProjectId
     * @param  array  $flowProcessData
     * @param  array  $statusEnumData
     * @param  int    $flowStatusId  // 复制至 功能增加, 默认不使用
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     *                               User Long
     *                               Date 2025/4/12
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId, array $flowProcessData, array $statusEnumData, int $flowStatusId = 0)
    {
        $oldProjectData = FlowStatusModel::status()
            ->with([
                'statusText' => function ($sql) {
                    $sql->where(['is_delete' => BaseModel::DELETE_NOT]);
                }
            ]);


        if ($flowStatusId) {
            $oldProjectData = $oldProjectData->where('flow_status_id', $flowStatusId);
        } else {
            $oldProjectData = $oldProjectData->where([
                'project_id' => $oldProjectId,
            ]);
        }

        $oldProjectData = $oldProjectData->select();

        $newFlowStatus = ['data' => [], 'is_rename' => false];
        if ($flowStatusId) {
            $diffData = FlowStatusModel::status()->where([
                'project_id'       => $newProjectId,
                'status_flow_name' => $oldProjectData->first()->status_flow_name
            ])->column('status_flow_name, project_id', 'project_id');
        }

        foreach ($oldProjectData as $oldProjectDatum) {
            $rename = $oldProjectDatum->status_flow_name;
            if (isset($diffData[$newProjectId])) {
                if ( ! $newFlowStatus['is_rename']) {
                    $newFlowStatus['is_rename'] = true;
                }
                $rename = FlowStatusModel::generateName($diffData[$newProjectId]['status_flow_name'], $diffData[$newProjectId]['project_id']);
            }
            // 复制工作流
            $flowStatusModel = new FlowStatusModel();
            $flowStatusModel->save([
                'attribution_id'   => $oldProjectDatum->flow_status_id,
                'flow_process_id'  => $flowProcessData[$oldProjectDatum->flow_process_id] ?? $oldProjectDatum->flow_process_id,
                'status_flow_name' => $rename,
                'status_flow_desc' => $oldProjectDatum->status_flow_desc,
                'flow_status_type' => $oldProjectDatum->flow_status_type,
                'project_id'       => $newProjectId
            ]);

            $newFlowStatus['flow_status_id'] = $flowStatusModel->flow_status_id;
            $newFlowStatus['attribution_id'] = $oldProjectDatum->flow_status_id;

            if (isset($diffData[$newProjectId])) {
                $newFlowStatus['data']['status_flow'][] = [
                    'flow_status_id'   => $flowStatusModel->flow_status_id,
                    'status_flow_name' => $rename,
                    'project_id'       => $newProjectId,
                    'url'              => self::RENAME_URL
                ];
            }
            foreach ($oldProjectDatum->statusText as $item) {
                // 复制工作流程节点
                $flowStatusTextModel = new FlowStatusTextModel();
                $flowStatusTextModel->save([
                    'attribution_id' => $item->status_text_id,
                    'flow_status_id' => $flowStatusModel->flow_status_id,
                    'status_enum_id' => $statusEnumData[$item->status_enum_id] ?? $item->status_enum_id,
                    'status_type'    => $item->status_type
                ]);
            }
        }

        return $newFlowStatus;
    }

    /**
     * 根据项目id查询 新旧工作流id
     * @param  int  $projectId
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectAttributionIdByProjectId(int $projectId)
    {
        return FlowStatusModel::status()->where([
            'project_id' => $projectId,
        ])->column('flow_status_id', 'attribution_id');
    }

    /**
     * 根据项目id查询 工作流id
     * @param  int  $projectId
     * @return array
     * User Long
     * Date 2024/10/15
     */
    public static function selectFlowStatusIdByProjectId(int $projectId)
    {
        return FlowStatusModel::status()->where([
            'project_id' => $projectId,
        ])->column('flow_status_id');
    }

    /**
     * 更新工作流名称
     * @param  int     $flowStatusId
     * @param  string  $statusFlowName
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public function rename(int $flowStatusId, string $statusFlowName)
    {
        $model = FlowStatusModel::findByStatusId($this->flowStatusType, $flowStatusId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        // 检查工作流名称是否已存在
        $statusFlowNameExist = FlowStatusModel::findStatusFlowAndName($model->project_id, $this->flowStatusType, $statusFlowName);
        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        if ($statusFlowNameExist && $statusFlowNameExist->flow_status_id != $flowStatusId) {
            $name = match ($projectInfo->is_template) {
                ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
                ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
                default => '',
            };
            throw new ParamsException($name.'-工作流名称不可相同');
        }

        // 校验名称是否与模板相同
        $this->validateProjectFlowStatusData($projectInfo, $statusFlowName);

        $model->status_flow_name = $statusFlowName;
        $model->save();
    }
}
