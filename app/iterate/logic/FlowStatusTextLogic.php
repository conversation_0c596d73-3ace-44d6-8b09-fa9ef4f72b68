<?php
/**
 * Desc 工作流状态描述 - 逻辑层
 * User Long
 * Date 2024/7/27
 */

namespace app\iterate\logic;

use app\infrastructure\logic\EnumLogic;
use app\infrastructure\model\EnumModel;
use app\iterate\model\FlowStatusTextModel;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseLogic;
use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use utils\Log;

class FlowStatusTextLogic extends BaseLogic
{
    /**
     * 保存状态描述
     * @param int $flowStatusId 流程状态id
     * @param int $statusEnumId 状态库Id
     * @param int $statusType 状态类型;1-开始,2-过程,3-结束
     * @param int $sort 排序
     * @return mixed|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/9
     */
    public static function saveFlowStatusTextData(int $flowStatusId, int $statusEnumId, int $statusType, int $sort): mixed
    {
        $model = new FlowStatusTextModel();

        // 判断是否查询到符合数据，查到编辑
        $res = $model::status()->where(['flow_status_id' => $flowStatusId, 'status_enum_id' => $statusEnumId])->find();
        if ($res) {
            $res->status_type = $statusType;
            $res->sort = $sort;
            $res->save();

            return $res->status_text_id;
        }

        // 新增状态描述
        $model->flow_status_id = $flowStatusId;
        $model->status_enum_id = $statusEnumId;
        $model->status_type = $statusType;
        $model->sort = $sort;
        $model->save();

        return $model->status_text_id;
    }


    /**
     * 根据状态流程id与状态描述id集查询出需要删除数据
     * @param int $flowStatusId
     * @param array $statusEnumIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public static function deleteNotFlowStatusByIds(int $flowStatusId, array $statusEnumIds): void
    {
        $notFlowStatusExist = FlowStatusTextModel::selectFlowStatusByIds($flowStatusId, $statusEnumIds);

        foreach ($notFlowStatusExist as $notFlowStatusModel) {
            $notFlowStatusModel->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 根据状态流程id查询出需要删除数据
     * @param int $flowStatusId
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/11
     */
    public static function deleteFlowStatusByStatusId(int $flowStatusId): void
    {
        $notFlowStatusExist = FlowStatusTextModel::selectFlowStatusByStatusId($flowStatusId);

        foreach ($notFlowStatusExist as $model) {
            $model->save(['is_delete' => BaseModel::DELETE_YES]);
        }
    }

    /**
     * 查询 工作流状态设置 数据
     * @param int $flowStatusId
     * @return FlowStatusTextModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public static function selectFlowStatusTextById(int $flowStatusId): array|Collection
    {
        return FlowStatusTextModel::status()
            ->field(FlowStatusTextModel::LIST_FIELDS)
            ->where(['flow_status_id' => $flowStatusId])
            ->with([
                // 获取状态库信息
                'enumDetail' => function($sql) {
                    $sql->bind(['status_enum_name' => 'name', 'colour']);
                },
                // 获取工作流程节点关系
                'flow_status_node_relation' => function($sql) use($flowStatusId) {
                    $sql->where(['flow_status_id' => $flowStatusId])
                        // 获取工作流程节点信息
                        ->with(['flow_process_node' => function($sql) {
                            $sql->bind(['process_node_name' => 'node_name']);
                        }])
                        ->append(['process_node_name'])
                        ->hidden(['id', 'flow_status_id', 'status_text_id']);
                },
                'flow_status_transfer' => function($sql) use($flowStatusId) {
                    $sql->where(['flow_status_id' => $flowStatusId])
                        // 获取工作流程节点信息
                        ->with(['enumDetail' => function($sql) {
                            $sql->bind(['target_status_enum_name' => 'name', 'colour']);
                        }])
                        ->hidden(['id', 'flow_status_id', 'status_text_id', 'status_enum_id']);
                }
            ])
            ->append(['status_type_text'])
            ->order('status_type ASC, sort ASC')
            ->select();
    }

    /**
     * 获取 工作流默认的开始状态 状态库id
     * @param int $flowStatusId
     * @return mixed
     * User Long
     * Date 2024/9/2
     */
    public static function getDefaultStatusEnumId(int $flowStatusId): mixed
    {
        return FlowStatusTextModel::status()->where([
            'flow_status_id' => $flowStatusId,
            'status_type' => FlowStatusTextModel::START_STATUS_TYPE
        ])->value('status_enum_id');
    }

    /**
     * 获取 工作流默认的开始状态 数据
     * @param int $flowStatusId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/11
     */
    public static function getDefaultStatusByFlowStatusId(int $flowStatusId): mixed
    {
        return FlowStatusTextModel::status()->where([
            'flow_status_id' => $flowStatusId,
            'status_type' => FlowStatusTextModel::START_STATUS_TYPE
        ])->find();
    }

    /**
     * 获取可流转至的状态数据
     * @param int $flowStatusId
     * @param int $statusEnumId
     * @param int $cntId
     * @return array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/8
     */
    public static function getTargetStatusEnum(int $flowStatusId, int $statusEnumId, int $cntId = 0): array|Collection
    {
        $res = [];
        $textData = FlowStatusTextModel::status()->where([
                    'flow_status_id' => $flowStatusId,
                    'status_enum_id' => $statusEnumId
                ])
                ->field('flow_status_id, status_text_id, status_enum_id')
                ->find();

        if (!$textData) return $res;
        $res = $textData->toArray();
        $res['status_enum_name'] = $res['colour'] = '';
        $res['flow_status_transfer'] = [];

        $statusEnumData = FlowStatusEnumLogic::findDemandByEnumId($res['status_enum_id']);
        if ($statusEnumData) {
            $res['status_enum_name'] = $statusEnumData['name'];
            $res['colour'] = $statusEnumData['colour'];
        }

        // 获取可流转至的状态数据
        $transferData = FlowStatusTransferLogic::getTargetStatusEnumByStatusTextId($res['status_text_id']);
        if ($transferData) {
            $transferData = $transferData->toArray();

            // 特殊处理需求，缺陷工作流处理人取值
            //// 查询工作项数据
            $workItemsData = WorkItemsLogic::getWorkItemsDetailByCntId($cntId);

            if ($workItemsData) {
                // 避免我处理人报错
                if (!isset($workItemsData['handler_uid'])) {
                    $workItemsData['handler_uid'] = [];
                }
                // 枚举使用场景
                $enumData = array_unique(array_merge(
                    array_column(EnumLogic::selectorEnumData(EnumModel::DEMAND_HANDLER), 'enum_value'),
                    array_column(EnumLogic::selectorEnumData(EnumModel::DEFECT_HANDLER), 'enum_value')
                ));

                try {
                    foreach ($transferData as &$transferDatum) {
                        foreach ($transferDatum['extends']['fieldData'] ?? [] as $key => &$fieldDatum) {
//                            if ($fieldDatum['field_name'] == 'handler_uid' && is_array($fieldDatum['default_value'])) {
//                                $handlerUid = $workItemsData[$fieldDatum['default_value'][0]] ?? [];
//                                $transferDatum['extends']['fieldData'][$key]['default_value'] = is_array($handlerUid) ? $handlerUid : [$handlerUid];
//                            }

                            // 2025-3-5修改，需求、缺陷 人员字段都需要转换
                            if ($fieldDatum['component_type'] == 'ApiSelect'
                                && isset($fieldDatum['field_component']['url'])
                                && (
                                    $fieldDatum['field_component']['url'] == '/devops/project/projectUser/selectorListQuery'
                                    || $fieldDatum['field_component']['url'] == '/project/projectUser/selectorListQuery'
                                )
                            ) {
                                $assignUser = [];
                                if (isset($fieldDatum['default_value'])) {
                                    if (is_array($fieldDatum['default_value'])) {
                                        foreach ($fieldDatum['default_value'] as $defaultValueDatum) {
                                            if (in_array($defaultValueDatum, $enumData)) {
                                                if (!empty($workItemsData[$defaultValueDatum])) {
                                                    if (is_array($workItemsData[$defaultValueDatum])) {
                                                        $assignUser = array_merge($assignUser, $workItemsData[$defaultValueDatum]);
                                                    } else {
                                                        $assignUser[] = $workItemsData[$defaultValueDatum];
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        if (in_array($fieldDatum['default_value'], $enumData)) {
                                            if (!empty($workItemsData[$fieldDatum['default_value']])) {
                                                if (is_array($workItemsData[$fieldDatum['default_value']])) {
                                                    $assignUser = array_merge($assignUser, $workItemsData[$fieldDatum['default_value']]);
                                                } else {
                                                    $assignUser[] = $workItemsData[$fieldDatum['default_value']];
                                                }
                                            }
                                        }
                                    }
                                }

                                if ($assignUser) {
                                    $assignUser = array_values(array_unique($assignUser));

                                    if (count($assignUser) == 1) {
                                        $assignUser = (int)implode('', $assignUser);
                                    }
                                }

                                if ($assignUser) {
                                    if ($fieldDatum['field_name'] == 'handler_uid') {
                                        $assignUser = is_array($assignUser) ? $assignUser : [$assignUser];
                                    }
                                    $transferDatum['extends']['fieldData'][$key]['default_value'] = $assignUser;
                                }

                                // 当默认值为空，处理人 取工作项的处理人合集，其他人员字段 取工作项的第一个处理人
                                if (empty($assignUser) && empty($transferDatum['extends']['fieldData'][$key]['default_value'])) {
                                    if ($fieldDatum['field_name'] == 'handler_uid') {
                                        $transferDatum['extends']['fieldData'][$key]['default_value'] = is_array($workItemsData['handler_uid']) ? $workItemsData['handler_uid'] : [$workItemsData['handler_uid']];
                                    } else {
                                        $transferDatum['extends']['fieldData'][$key]['default_value'] = $workItemsData['handler_uid'][0] ?? 0;
                                    }
                                }
                            }
                        }
                    }

                    unset($transferDatum, $fieldDatum, $defaultValueDatum);
                } catch (\Throwable $e) {
                    Log::instance(Log::PLATFORM)->error($e);
                }
            }

            $res['flow_status_transfer'] = $transferData;
        }

        return $res;
    }

    /**
     * 根据流程状态id查询 状态描述id
     * @param array $flowStatusIds
     * @return array
     * User Long
     * Date 2024/10/14
     */
    public static function selectAttributionIdByFlowStatusIds(array $flowStatusIds)
    {
        return FlowStatusTextModel::status()->whereIn('flow_status_id', $flowStatusIds)->column('status_text_id', 'attribution_id');
    }

    /**
     * 根据状态描述id 查询数据
     * @param int $statusTextId
     * @return FlowStatusTextModel|array|mixed|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/26
     */
    public static function findDataByFlowTextId(int $statusTextId)
    {
        return FlowStatusTextModel::status()->whereIn('status_text_id', $statusTextId)->find();
    }

    /**
     * 根据流程状态id查询 状态描述id
     * @param int $flowStatusId
     * @return FlowStatusTextModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/25
     */
    public static function selectorFlowStatusByIterationId(int $flowStatusId)
    {
        return FlowStatusTextModel::status()
            ->with(['enumDetail' => function($sql) {
                $sql->bind(['label' => 'name']);
            }])
            ->where(['flow_status_id' => $flowStatusId])
            ->field('status_text_id as value, status_enum_id')
            ->hidden(['status_enum_id'])
            ->select();
    }

    /**
     * 获取状态类型为结束的 状态描述id
     * @return array
     * User Long
     * Date 2025/2/18
     */
    public static function getFlowTextIdByStatusTypeOff()
    {
        return FlowStatusTextModel::status()
            ->where(['status_type' => FlowStatusTextModel::END_STATUS_TYPE])
            ->column('status_text_id');
    }
}
