<?php
/**
* Desc 工作流状态描述 - 模型
* User Long
* Date 2024/08/07*/

declare (strict_types = 1);

namespace app\iterate\model;

use basic\BaseModel;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;

/**
* This is the model class for table "flow_status_text".
* @property string $status_text_id id
* @property string $create_by 创建人
* @property string $create_by_name 创建人名称
* @property string $create_at 创建时间
* @property string $is_delete 是否删除;1-是 0-否
* @property string $update_by 更新人
* @property string $update_by_name 更新人名称
* @property string $update_at 更新时间
* @property string $flow_status_id 流程状态id
* @property string $status_enum_id 状态库Id
* @property string $status_type 状态类型;1-开始,2-过程,3-结束
* @property string $sort 排序，默认正序
*/
class FlowStatusTextModel extends BaseModel
{
    use CreateAndUpdateModelTrait;

    protected $pk = 'status_text_id';
    protected $name = 'flow_status_text';

    const LIST_FIELDS = 'status_text_id, status_enum_id, status_type, sort';

    const START_STATUS_TYPE = 1;
    const PROCESS_STATUS_TYPE = 2;
    const END_STATUS_TYPE = 3;
    const STATUS_TYPE_TEXT = [
        self::START_STATUS_TYPE => '开始状态',
        self::PROCESS_STATUS_TYPE => '过程状态',
        self::END_STATUS_TYPE => '结束状态'
    ];

    /**
     * 预加载 - 状态流程
     * @return HasOne
     * User Long
     * Date 2024/8/8
     */
    public function flowStatusDetail(): HasOne
    {
        return $this->hasOne(FlowStatusModel::class, 'flow_status_id', 'flow_status_id');
    }

    /**
     * 根据状态流程id与状态id集查询数据
     * @param int $flowStatusId
     * @param array $statusEnumIds
     * @return FlowStatusTextModel[]|array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/10
     */
    public static function selectFlowStatusByIds(int $flowStatusId, array $statusEnumIds): array|Collection
    {
        return static::status()->where(['flow_status_id' => $flowStatusId])->whereNotIn('status_enum_id', $statusEnumIds)->select();
    }

    /**
     * 根据状态流程id集查询数据
     * @param int $flowStatusId
     * @return array|Collection
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/11
     */
    public static function selectFlowStatusByStatusId(int $flowStatusId)
    {
        return static::status()->where(['flow_status_id' => $flowStatusId])->select();
    }

    /**
     * 获取器 - 获取状态类型
     * @param $value
     * @param $data
     * @return string
     * User Long
     * Date 2024/8/10
     */
    public function getStatusTypeTextAttr($value, $data): string
    {
        return self::STATUS_TYPE_TEXT[$data['status_type']] ?? '未知状态';
    }

    /**
     * 预加载 - 状态库
     * @return HasOne
     * User Long
     * Date 2024/7/24
     */
    public function enumDetail(): HasOne
    {
        return $this->hasOne(FlowStatusEnumModel::class, 'status_enum_id', 'status_enum_id');
    }

    /**
     * 预加载 - 状态与节点关系
     * @return HasMany
     * User Long
     * Date 2024/8/10
     */
    public function flowStatusNodeRelation(): HasMany
    {
        return $this->hasMany(FlowStatusNodeRelationModel::class, 'status_text_id', 'status_text_id');
    }

    /**
     * 预加载 - 状态流转关系
     * @return HasMany
     * User Long
     * Date 2024/8/10
     */
    public function flowStatusTransfer(): HasMany
    {
        return $this->hasMany(FlowStatusTransferModel::class, 'status_text_id', 'status_text_id');
    }
}
