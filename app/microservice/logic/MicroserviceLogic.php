<?php
declare (strict_types=1);

namespace app\microservice\logic;

use app\microservice\model\MicroserviceModel;
use app\microservice\validate\MicroserviceValidate;
use app\product\logic\ProductMicroserviceLogic;
use app\project\logic\ProjectInfoLogic;
use basic\BaseModel;
use exception\NotFoundException;
use basic\BaseLogic;

class MicroserviceLogic extends BaseLogic
{
    /**
     * 新增
     * @param $params
     * @return MicroserviceModel
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function create($params)
    {

        validate(MicroserviceValidate::class)->scene('create')->check($params);

        $model = new MicroserviceModel();
        $model->save($params);

        return $model->toDetail();
    }


    /**
     * 删除
     * @param $microserviceId
     * @return void
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function delete($microserviceId)
    {
        $model = MicroserviceModel::findById($microserviceId);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->is_delete = BaseModel::DELETE_YES;
        $model->save();

        // 解除微服务与产品关联
        ProductMicroserviceLogic::removeMicroserviceBindByMicroserviceId([$microserviceId]);
    }

    /**
     * 修改
     * @param $params
     * @return MicroserviceModel|array|mixed|\think\Model
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function update($params)
    {

        validate(MicroserviceValidate::class)->scene('update')->check($params);
        $model = MicroserviceModel::findById($params['microservice_id']);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->save($params);

        return $model->toDetail();
    }


    /**
     * 详情
     * @param $id
     * @return MicroserviceModel
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function detail($id)
    {
        $model = MicroserviceModel::findById($id);
        if (!$model) {
            throw new NotFoundException();
        }
        $model->getFieldList();

        return $model->toDetail();
    }

    /**
     * 分页查询
     * @param $params
     * @return \think\Paginator
     * @throws \think\db\exception\DbException
     * <AUTHOR>
     * @date 2024/7/8 上午10:18
     */
    public function pageQuery($params)
    {
        $where = [];
        if ($params['microservice_name'] ?? '') {
            $where[] = ['microservice_name', 'like', "%{$params['microservice_name']}%"];
        }
        return (new MicroserviceModel)
            ->where(['is_delete' => BaseModel::DELETE_NOT])
            ->where($where)
            ->field(MicroserviceModel::LIST_FIELDS)->order('is_enable DESC, microservice_id desc')
            ->paginate(getPageSize());

    }

    /**
     *  启用
     * @param $microserviceId
     * @return MicroserviceModel|void
     * <AUTHOR>
     * @date 2024/7/8 上午9:53
     */
    public function enable($microserviceId)
    {
        $model = MicroserviceModel::findById($microserviceId);
        if (!$model) {
            throw new NotFoundException();
        }
        if ($model->isEnable()) {
            return;
        }

        $model->is_enable = MicroserviceModel::ENABLE_YES;
        $model->save();

    }

    /**
     * 禁用
     * @param $microserviceId
     * @return MicroserviceModel|void
     * <AUTHOR>
     * @date 2024/7/8 上午9:54
     */
    public function disable($microserviceId)
    {
        $model = MicroserviceModel::findById($microserviceId);
        if (!$model) {
            throw new NotFoundException();
        }
        if (!$model->isEnable()) {
            return;
        }
        $model->is_enable = MicroserviceModel::ENABLE_NOT;
        $model->save();
    }

    /**
     * 根据微服务id集查询数据
     * @param array $microserviceIds
     * @return MicroserviceModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/7/25
     */
    public static function selectInfoByClientIds(array $microserviceIds, int $is_enable = null)
    {
        $model = MicroserviceModel::status()->whereIn('microservice_id', $microserviceIds);

        if ($is_enable !== null) $model->where(['is_enable' => $is_enable]);

        return $model->select();
    }

    /**
     * 根据微服务名称模糊获取微服务id集合
     * @param string $microserviceName 微服务名称
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/7/31
     */
    public static function getMicroserviceIdByMicroserviceName(string $microserviceName)
    {
        $microserviceInfo = MicroserviceModel::status()->where('microservice_name', 'like', "%{$microserviceName}%")->select();

        if ($microserviceInfo->isEmpty()) {
            return [];
        }

        return $microserviceInfo->column('microservice_id');
    }

    /**
     * 微服务下拉框数据
     * @return array
     * User Long
     * Date 2024/7/26
     */
    public function selector()
    {
        return MicroserviceModel::status()->where(['is_enable' => MicroserviceModel::ENABLE_YES])->order('microservice_id DESC')->column('microservice_name as label, microservice_id as value');
    }

    /**
     * 项目产品微服务下拉框数据
     * @param int $projectId
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2024/11/20
     */
    public function selectorByProduct(int $projectId)
    {
        $projectModel = ProjectInfoLogic::getProjectData($projectId);

        if (!$projectModel) {
            return [];
        }

        return ProductMicroserviceLogic::getMicroservice($projectModel->product_id, BaseModel::ENABLE_YES);
    }
}
