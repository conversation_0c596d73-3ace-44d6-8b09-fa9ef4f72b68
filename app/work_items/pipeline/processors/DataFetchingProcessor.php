<?php

declare(strict_types=1);

namespace app\work_items\pipeline\processors;

use app\work_items\logic\WorkItemsEsLogic;
use app\work_items\model\WorkItemsModel;
use app\work_items\pipeline\DataContainer;
use app\work_items\pipeline\WorkItemProcessingStepInterface;
use Elastic\Elasticsearch\Client as EsClient; // EsClient 是 Elasticsearch 客户端的别名
use think\facade\Env;

/**
 * Class DataFetchingProcessor
 * @package app\work_items\pipeline\processors
 *
 * 负责执行Elasticsearch查询，获取主数据和全局统计信息。
 */
class DataFetchingProcessor implements WorkItemProcessingStepInterface
{
    private EsClient $esClient;
    private mixed $esIndex;
    private WorkItemsEsLogic $workItemsEsLogicInstance; // 用于访问常量和配置

    public function __construct()
    {
        // 理想情况下，EsClient 和 WorkItemsEsLogic 实例应通过依赖注入传入。
        // 为简化，这里暂时直接获取。
        $this->workItemsEsLogicInstance = WorkItemsEsLogic::getInstance();
        // 假设 WorkItemsEsLogic 有一个公共方法来获取ES客户端，或者EsClientFactory可直接使用
        // 如果 WorkItemsEsLogic 内部管理 $esClient，我们需要一种方式来访问它。
        // 暂时通过 EsClientFactory 获取，这与 WorkItemsEsLogic 内部的初始化方式一致。
        $this->esClient = \utils\EsClientFactory::getInstance();
        $this->esIndex = Env::get('es.content_index');
    }

    /**
     * 执行ES查询，提取数据和统计信息。
     *
     * @param DataContainer $dataContainer 数据容器。
     * @param array $params 额外参数（当前未使用）。
     * @return DataContainer 处理后的数据容器。
     * @throws \Elastic\Elasticsearch\Exception\ClientResponseException
     * @throws \Elastic\Elasticsearch\Exception\ServerResponseException
     */
    public function process(DataContainer $dataContainer, array $params = []): DataContainer
    {
        $esQueryBool = $dataContainer->esQuery;
        $mainDataSort = $dataContainer->mainDataSort;
        $mainDataGroupBy = $dataContainer->mainDataGroupBy;
        $processedFieldList = $dataContainer->processedFieldList;

        $esRequest = [
            'index' => $this->esIndex,
            'body'  => [
                'query' => ['bool' => $esQueryBool],
                'aggs'  => [ // 全局统计信息聚合
                    'demand_count_agg'             => ['filter' => ['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_DEMAND]]],
                    'task_count_agg'               => ['filter' => ['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_TASK]]],
                    'flaw_count_agg'               => ['filter' => ['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_FLAW]]],
                    'demand_end_count_agg'         => ['filter' => ['bool' => ['must' => [['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_DEMAND]], ['term' => ['isEnd' => true]]]]]],
                    'task_end_count_agg'           => ['filter' => ['bool' => ['must' => [['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_TASK]], ['term' => ['isEnd' => true]]]]]],
                    'flaw_end_count_agg'           => ['filter' => ['bool' => ['must' => [['term' => ['cnt_type' => WorkItemsModel::CNT_TYPE_FLAW]], ['term' => ['isEnd' => true]]]]]],
                    'estimated_work_hours_sum_agg' => ['sum' => ['field' => 'estimated_work_hours']],
                    'actual_work_hours_sum_agg'    => ['sum' => ['field' => 'actual_work_hours']],
                    'remaining_work_sum_agg'       => ['sum' => ['field' => 'remaining_work']],
                ]
            ]
        ];

        $initialMainDataFlat = [];
        $rawGroupedDataFromEs = []; // ES原始分组数据结构

        if ($mainDataGroupBy) {
            $esRequest['body']['size'] = 0;
            $esRequest['body']['aggs']['grouped_main_data'] = [
                'terms' => [
                    'field'   => $mainDataGroupBy,
                    'size'    => WorkItemsEsLogic::MAX_INNER_RESULT_WINDOW,
                    'missing' => WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY
                ],
                'aggs'  => [
                    'docs_in_group' => [
                        'top_hits' => [
                            'size'    => WorkItemsEsLogic::MAX_INNER_RESULT_WINDOW,
                            'sort'    => !empty($mainDataSort) ? $mainDataSort : [['_doc' => 'asc']],
                            '_source' => $processedFieldList
                        ]
                    ]
                ]
            ];
        } else {
            $esRequest['body']['size'] = WorkItemsEsLogic::NOT_PAGE_MAX;
            $esRequest['body']['sort'] = !empty($mainDataSort) ? $mainDataSort : [['_doc' => 'asc']];
            if (!empty($processedFieldList)) {
                $esRequest['body']['_source'] = $processedFieldList;
            }
        }

        $response = $this->esClient->search($esRequest)->getBody();
        $resp = json_decode((string)$response, true);
        $dataContainer->rawEsResponse = $resp;

        $statsAggs = $resp['aggregations'] ?? [];
        $dataContainer->statistics = [
            'demand_count'         => $statsAggs['demand_count_agg']['doc_count'] ?? 0,
            'task_count'           => $statsAggs['task_count_agg']['doc_count'] ?? 0,
            'flaw_count'           => $statsAggs['flaw_count_agg']['doc_count'] ?? 0,
            'demand_end_count'     => $statsAggs['demand_end_count_agg']['doc_count'] ?? 0,
            'task_end_count'       => $statsAggs['task_end_count_agg']['doc_count'] ?? 0,
            'flaw_end_count'       => $statsAggs['flaw_end_count_agg']['doc_count'] ?? 0,
            'estimated_work_hours' => (float)number_format($statsAggs['estimated_work_hours_sum_agg']['value'] ?? 0, 2, '.', ''),
            'actual_work_hours'    => (float)number_format($statsAggs['actual_work_hours_sum_agg']['value'] ?? 0, 2, '.', ''),
            'remaining_work'       => (float)number_format($statsAggs['remaining_work_sum_agg']['value'] ?? 0, 2, '.', ''),
        ];

        if ($mainDataGroupBy) {
            $buckets = $resp['aggregations']['grouped_main_data']['buckets'] ?? [];
            foreach ($buckets as $bucket) {
                $groupKey = $bucket['key'] === WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY ? '_default_group_' : (string)$bucket['key'];
                $itemsInGroup = array_column($bucket['docs_in_group']['hits']['hits'] ?? [], '_source');
                $rawGroupedDataFromEs[$groupKey] = $itemsInGroup; // 存储原始分组项目
                $initialMainDataFlat = array_merge($initialMainDataFlat, $itemsInGroup);
            }
        } else {
            $initialMainDataFlat = array_column($resp['hits']['hits'] ?? [], '_source');
            // 对于非分组情况，也填充 rawGroupedDataFromEs 以保持一致性，供后续处理器使用
            $rawGroupedDataFromEs[WorkItemsEsLogic::DEFAULT_ES_GROUP_MISSING_KEY] = $initialMainDataFlat;
        }
        
        // 确保扁平列表中的项目唯一
        $tempFlatMap = [];
        foreach ($initialMainDataFlat as $item) {
            if (isset($item['cnt_id'])) {
                $tempFlatMap[$item['cnt_id']] = $item;
            }
        }
        $dataContainer->flatListData = array_values($tempFlatMap);
        $dataContainer->groupedData = $rawGroupedDataFromEs; // 存储从ES获取的原始分组结构

        return $dataContainer;
    }
}