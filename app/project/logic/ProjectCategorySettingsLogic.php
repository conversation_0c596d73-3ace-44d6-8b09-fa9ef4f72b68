<?php
/**
 * Desc 迭代分类 - 逻辑层
 * User Long
 * Date 2024/07/19
 */
declare (strict_types=1);

namespace app\project\logic;

use app\infrastructure\logic\FieldConfigLogic;
use app\infrastructure\logic\TemplateLogic;
use app\iterate\logic\FlowProcessLogic;
use app\iterate\logic\FlowProcessNodeRelationLogic;
use app\iterate\logic\FlowStatusEnumLogic;
use app\iterate\logic\FlowStatusLogic;
use app\iterate\logic\FlowStatusNodeRelationLogic;
use app\iterate\logic\FlowStatusTextLogic;
use app\iterate\logic\FlowStatusTransferLogic;
use app\project\model\ProjectCategorySettingsModel;
use app\project\model\ProjectModel;
use app\project\model\ProjectTemplateModel;
use app\project\validate\ProjectCategorySettingsValidate;
use app\work_items\logic\WorkItemsLogic;
use basic\BaseLogic;
use basic\BaseModel;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Elastic\Elasticsearch\Exception\MissingParameterException;
use Elastic\Elasticsearch\Exception\ServerResponseException;
use exception\NotFoundException;
use exception\ParamsException;
use think\Collection;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;
use Throwable;
use utils\DBTransaction;

class ProjectCategorySettingsLogic extends BaseLogic
{
    // 重命名链接
    private const RENAME_URL = '/project/category/rename';
    private int $projectCategorySettingsType; // 分类所属 1=>迭代 2=>需求 3=>任务 4=>缺陷 5=>测试用例 6=>测试计划
    private string $projectCategoryValidateName = ''; // 验证器模块名 1=>Iteration 2=>Demand 3=>Task 4=>缺陷 5=>测试用例 6=>测试计划
    private int $flowStatusType; // 工作流类型 1-迭代,2-需求
    private int $categoryMaxNum = 10; // 类别数量最大限制：最多可添加10个类别

    public function __construct(int $projectCategorySettingsType = BaseModel::SETTING_TYPE_ITERATION)
    {
        $this->projectCategorySettingsType = $projectCategorySettingsType;
        $this->flowStatusType = $projectCategorySettingsType;

        // 根据分类所属，设置验证器模块名，工作流类型
        switch ($this->projectCategorySettingsType) {
            case BaseModel::SETTING_TYPE_ITERATION:
                $this->projectCategoryValidateName = 'Iteration';
                break;
            case BaseModel::SETTING_TYPE_DEMAND:
            case BaseModel::SETTING_TYPE_DEFECT:
                $this->projectCategoryValidateName = 'WorkItem';
                break;
            case BaseModel::SETTING_TYPE_TASK:
                $this->projectCategoryValidateName = 'Task';
                break;
            default:
                break;
        }
    }

    /**
     * 校验工作流信息
     * @param $projectId
     * @param int $flowStatusId 工作流id
     * @param int $flowProcessId 工作流程id
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/22
     */
    private function validateFlowStatusData($projectId, int $flowStatusId, int $flowProcessId): void
    {
        if (!$flowStatusId) {
            return;
        }
        $flowStatusData = FlowStatusLogic::findFlowStatusById($this->flowStatusType, $flowStatusId);
        if (!$flowStatusData) {
            throw new NotFoundException('当前工作流已被删除，请刷新页面后重新选择');
        }
        if ($flowStatusData->project_id != $projectId) {
            throw new ParamsException('不可使用非本项目的页面模版及流程数据');
        }
        if ($flowStatusData->flow_status_type != $this->flowStatusType) {
            throw new ParamsException('当前工作流所属非迭代模块，请联系管理员查实');
        }
        if ($flowProcessId && $flowStatusData->flow_process_id != $flowProcessId) {
            throw new ParamsException('工作流与工作流程关联异常，请刷新页面后重新添加');
        }
    }

    /**
     * 校验名称是否与模板相同
     * @param $projectInfo
     * @param string $categoryName
     * @param string $categoryEnName
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function validateProjectTemplateData($projectInfo, string $categoryName = '', string $categoryEnName = ''): void
    {
        if ($projectInfo) {
            if ($projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $templateId = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);

                if ($categoryName && $templateId) {
                    $categoryTemplateNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndName($templateId, $this->projectCategorySettingsType, $categoryName);
                    if ($categoryTemplateNameExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id).'-类别名称已存在');
                    }
                }

                if ($categoryEnName && $templateId) {
                    $categoryEnTemplateNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndEnName($templateId, $this->projectCategorySettingsType, $categoryEnName);
                    if ($categoryEnTemplateNameExist) {
                        throw new ParamsException(ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id).'-图标设置名称已存在');
                    }
                }
            } else {
                $projectIds = ProjectInfoLogic::getProjectIdsByTemplateId($projectInfo->project_template_id)
                    ->where('is_template',  ProjectTemplateModel::IS_TEMPLATE_NO);
                $categoryTemplateNameExist = ProjectCategorySettingsModel::status()
                    ->with(['project' => function($sql) {
                        $sql->bind(['project_name']);
                    }])
                    ->where([
                        'project_category_settings_type' => $this->projectCategorySettingsType,
                    ])
                    ->whereIn('project_id', $projectIds->column('project_id'))
                    ->select();

                if ($categoryName && !$categoryTemplateNameExist->where('category_name', $categoryName)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $categoryTemplateNameExist->where('category_name', $categoryName)->column('project_name')).'】-类别名称已存在');
                }

                if ($categoryName && !$categoryTemplateNameExist->where('category_en_name', $categoryName)->isEmpty()) {
                    throw new ParamsException('【'.implode('】、【', $categoryTemplateNameExist->where('category_en_name', $categoryName)->column('project_name')).'】-图标设置名称已存在');
                }
            }
        }
    }

    /**
     * 校验页面模版是否异常
     * @param $projectId
     * @param $templateId
     * @return void
     */
    private function validateTemplatePageData($projectId, $templateId): void
    {
        $templateInfo = (new TemplateLogic())->findById($templateId);

        if (!$templateInfo) {
            throw new NotFoundException('当前页面模板已被删除，请刷新页面后重新选择');
        }

        if ($templateInfo->project_id != $projectId) {
            throw new ParamsException('不可使用非本项目的页面模版及流程数据');
        }
    }

    /**
     * 插入一条记录
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function create(array $params): void
    {
        // 验证器校验
        validate(ProjectCategorySettingsValidate::class)->scene('create' . $this->projectCategoryValidateName)->check($params);

        if (ProjectCategorySettingsModel::countProjectCategory($params['project_id'], $this->projectCategorySettingsType) >= $this->categoryMaxNum) {
            throw new ParamsException('仅限添加' . $this->categoryMaxNum . '个类别');
        }

        $projectInfo = ProjectInfoLogic::getProjectData($params['project_id']);
        $name = match ($projectInfo->is_template) {
            ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
            ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
            default => '',
        };

        $categoryNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndName($params['project_id'], $this->projectCategorySettingsType, $params['category_name']);
        if ($categoryNameExist) {
            throw new ParamsException($name .'-类别名称已存在');
        }

        $categoryEnNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndEnName($params['project_id'], $this->projectCategorySettingsType, $params['category_en_name']);
        if ($categoryEnNameExist) {
            throw new ParamsException($name .'图标设置名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectTemplateData($projectInfo, $params['category_name'], $params['category_en_name']);

        // 校验页面模版是否异常
        $this->validateTemplatePageData($params['project_id'], $params['template_id']);

        // 校验工作流信息
        $this->validateFlowStatusData($params['project_id'], $params['flow_status_id'] ?? 0, $params['flow_process_id'] ?? 0);

        $model = new ProjectCategorySettingsModel();
        $params['project_category_settings_type'] = $this->projectCategorySettingsType; // 设置类别归属
        $model->save($params);
    }

    /**
     * 根据分类id集删除分类
     * @param array $categoryIds
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2024/7/22
     */
    public function delete(array $categoryIds): void
    {
        $models = ProjectCategorySettingsModel::selectByCategoryIds($this->projectCategorySettingsType, $categoryIds);

        try {
            Db::startTrans();

            foreach ($models as $model) {
                if ($model->is_enable) {
                    throw new ParamsException('类别开启中，不可进行删除');
                }
                $model->save(['is_delete' => BaseModel::DELETE_YES]);
            }
            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 根据分类id更新数据
     * @param $categoryId
     * @param array $params
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws ClientResponseException
     * @throws MissingParameterException
     * @throws ServerResponseException
     * @throws Throwable
     * User Long
     * Date 2024/7/22
     */
    public function update($categoryId, array $params): void
    {
        // 验证器校验
        validate(ProjectCategorySettingsValidate::class)->scene('update' . $this->projectCategoryValidateName)->check($params);

        $model = ProjectCategorySettingsModel::findByCategoryId($this->projectCategorySettingsType, $categoryId);
        if (!$model) {
            throw new NotFoundException();
        }

        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        $name = match ($projectInfo->is_template) {
            ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
            ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
            default => '',
        };

        $categoryNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndName($model->project_id, $this->projectCategorySettingsType, $params['category_name']);
        if ($categoryNameExist && $categoryNameExist->project_category_settings_id != $categoryId) {
            throw new ParamsException($name . '分类名称已存在');
        }

        $categoryEnNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndEnName($model->project_id, $this->projectCategorySettingsType, $params['category_en_name']);
        if ($categoryEnNameExist && $categoryEnNameExist->project_category_settings_id != $categoryId) {
            throw new ParamsException($name . '图标设置名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectTemplateData($projectInfo, $params['category_name'], $params['category_en_name']);

        // 校验页面模版是否异常
        $this->validateTemplatePageData($model->project_id, $params['template_id']);

        // 校验工作流信息
        $this->validateFlowStatusData($model->project_id, $params['flow_status_id'] ?? 0, $params['flow_process_id'] ?? 0);

        // 当工作流id发生变更，查询工作项id供后续修改
        $workItemIds = [];
        if (($this->isDemand() || $this->isDefect()) && $model->flow_status_id != $params['flow_status_id']) {
            $workItemIds = WorkItemsLogic::getItemByCategorySettingId($this->projectCategorySettingsType, $model->project_category_settings_id);
        }

        try {
            Db::startTrans();

            $model->save($params);

            // 修改工作项的工作流状态
            if ($workItemIds) {
                $flowStatus['flow_status_id'] = $params['flow_status_id'];
                $flowStatus['status_enum_id'] = FlowStatusTextLogic::getDefaultStatusEnumId($params['flow_status_id']);

                (new WorkItemsLogic)->setItemsCategorySettingId($workItemIds, $model->project_category_settings_id, $flowStatus);
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();

            throw $e;
        }
    }

    /**
     * 迭代分类详情
     * @param int $categoryId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException User Long
     * Date 2024/7/20
     */
    public function detail(int $categoryId): array
    {
        $model = ProjectCategorySettingsModel::findByCategoryId($this->projectCategorySettingsType, $categoryId);

        if (!$model) {
            throw new NotFoundException();
        }

        $model->hidden(['is_delete']);
        $res = $model->toArray();

        /** 预设字段 */
        $res['template_name'] = '';
        $res['status_flow_name'] = '';
        $res['flow_process_name'] = '';

        $templateInfo = (new TemplateLogic())->findById($res['template_id']);
        if ($templateInfo) {
            $res['template_name'] = $templateInfo['template_name'];
        }

        // 无工作流类型，直接返回
        if (!$this->flowStatusType) return $res;

        $statusFlowInfo = FlowStatusLogic::findFlowStatusById($this->flowStatusType, $model->flow_status_id);
        if ($statusFlowInfo) {
            $res['status_flow_name'] = $statusFlowInfo->status_flow_name;

            $flowProcessInfo = FlowProcessLogic::findProcessById($statusFlowInfo->flow_process_id);
            if ($flowProcessInfo) {
                $res['flow_process_name'] = $flowProcessInfo->flow_process_name;
            }
        }

        return $res;
    }

    /**
     * 分类分页
     * @param int $projectId
     * @return Collection
     * @throws DbException
     * User Long
     * Date 2024/7/20
     */
    public function listQuery(int $projectId): Collection
    {
        $projectIds[] = $projectId;
        $templateIds = [];

        $projectInfo = ProjectInfoLogic::getProjectData($projectId);
        if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
            $templateIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id);
        }
        $projectIds = array_merge($projectIds, $templateIds);

        return ProjectCategorySettingsModel::status()
            ->with([
                'template' => function ($sql) {
                    $sql->bind(['template_name']);
                },
                'flowStatus' => function ($sql) {
                    $sql->bind(['status_flow_name']);
                },
                'flowProcess' => function ($sql) {
                    $sql->bind(['flow_process_name']);
                }
            ])
//            ->where(['project_id' => $projectId, 'project_category_settings_type' => $this->projectCategorySettingsType])
            ->where(['project_category_settings_type' => $this->projectCategorySettingsType])
            ->whereIn('project_id', $projectIds)
            ->hidden(ProjectCategorySettingsModel::HIDDEN_FIELD)
            ->order('project_id ASC, is_enable DESC, sort ASC, create_at DESC')
            ->select()
            ->each(function ($item) use ($projectInfo, $templateIds) {
                /** 预设字段 */
                $item['template_name'] = $item['template_name'] ?: '';
                $item['status_flow_name'] = $item['status_flow_name'] ?: '';
                $item['flow_process_name'] = $item['flow_process_name'] ?: '';

                $item['template_not_allowed_del'] = false; // 模板字段不允许删除，默认否
                // 项目内模板字段不允许编辑
                if (isset($projectInfo->is_template) && $projectInfo->is_template == 0 && in_array($item['project_id'], $templateIds)) {
                    $item['template_not_allowed_del'] = true;
                }

                return $item;
            });
    }

    /**
     * 分类 - 启用
     * @param array $categoryIds 迭代分类ids
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function enable(array $categoryIds): void
    {

        $data = ProjectCategorySettingsModel::selectByCategoryIds($this->projectCategorySettingsType, $categoryIds);

        foreach ($data as $model) {
            $model->save(['is_enable' => BaseModel::ENABLE_YES]);
        }

    }

    /**
     * 分类 - 禁用
     * @param array $categoryIds 迭代分类ids
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/7/20
     */
    public function disable(array $categoryIds): void
    {

        $data = ProjectCategorySettingsModel::selectByCategoryIds($this->projectCategorySettingsType, $categoryIds);

        foreach ($data as $datum) {
            $datum->save(['is_enable' => $datum::ENABLE_NOT]);
        }

    }

    /**
     * 更新数据排序
     * @param array $sortData
     *  $sortData[] = ['category_id', 'sort']
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/8/23
     */
    public function updateSort(array $sortData): void
    {
        if (!$sortData) {
            return;
        }

        foreach ($sortData as $param) {
            // 验证器校验
            validate(ProjectCategorySettingsValidate::class)->scene('updateSort')->check($param);
        }

        $sortData = array_column($sortData, 'sort', 'category_id');
        $categoryIds = array_keys($sortData);
        $data = ProjectCategorySettingsModel::selectByCategoryIds($this->projectCategorySettingsType, $categoryIds);

        foreach ($data as $model) {
            if (isset($sortData[$model->project_category_settings_id])) {
                $model->save(['sort' => $sortData[$model->project_category_settings_id]]);
            }

        }
    }

    /**
     * 需求类别下拉数据
     * @param int|array $projectId
     * @return array
     * User Long
     * Date 2024/8/28
     */
    public function selector(int|array $projectId,$scenario=0): array
    {
        // 获取查询类别
        $where[] = match ($this->projectCategorySettingsType) {
            BaseModel::SETTING_TYPE_AGG => ['project_category_settings_type', 'in', [BaseModel::SETTING_TYPE_DEMAND, BaseModel::SETTING_TYPE_TASK, BaseModel::SETTING_TYPE_DEFECT]],
            default => ['project_category_settings_type', '=', $this->projectCategorySettingsType],
        };

        $is_enable = [
//                'project_category_settings_type' => $this->projectCategorySettingsType,
            'is_enable' => ProjectCategorySettingsModel::IS_ENABLE,
            'is_delete' => ProjectCategorySettingsModel::DELETE_NOT
        ];
        if ($scenario) {
            $is_enable = [];
        }
        $model = (new  ProjectCategorySettingsModel)
            ->where($is_enable)
            ->where($where);

        // 兼容多项目查询
        if (is_array($projectId)) {
            $projectIds = $projectId;

            $projectInfo = ProjectInfoLogic::getProjectDataSet($projectId)->where('is_template', 0);
            if (!$projectInfo->isEmpty()) {
                $templateIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($projectInfo->column('project_template_id'), $scenario);

                $projectIds = array_values(array_unique(array_merge($projectIds, $templateIds)));
            }
        } else {
            $projectIds[] = $projectId;

            $projectInfo = ProjectInfoLogic::getProjectData($projectId);
            if ($projectInfo && $projectInfo->is_template == ProjectTemplateModel::IS_TEMPLATE_NO) {
                $projectIds[] = ProjectTemplateLogic::getProjectIdByProjectTemplateId($projectInfo->project_template_id, $scenario);
            }
        }
        $model = $model->whereIn('project_id', $projectIds);

        return $model->order('project_category_settings_type ASC, is_enable DESC, sort ASC, create_at DESC')
            ->column('project_category_settings_id, project_category_settings_type, category_name, category_en_name, icon, template_id, flow_status_id,project_id');
    }

    /**
     * 需求类别下拉数据（带工作流状态）
     * @param  $projectId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/2
     */
    public function selectorAndFlowStatus($projectId,$scenario=0): array
    {
        if (!is_array($projectId)) {
            $projectId = [$projectId];
        }

        // 获取查询类别
        $where[] = match ($this->projectCategorySettingsType) {
            BaseModel::SETTING_TYPE_AGG => ['project_category_settings_type', 'in', [BaseModel::SETTING_TYPE_DEMAND, BaseModel::SETTING_TYPE_TASK, BaseModel::SETTING_TYPE_DEFECT]],
            default => ['project_category_settings_type', '=', $this->projectCategorySettingsType],
        };

        $is_enable = [
            'is_enable' => ProjectCategorySettingsModel::IS_ENABLE
        ];
        if ($scenario) {
            $is_enable = [];
        }

        $model = ProjectCategorySettingsModel::status();

        // 兼容多项目查询
        $projectInfo = ProjectInfoLogic::getProjectDataSet($projectId)->where('is_template', 0);
        if (!$projectInfo->isEmpty()) {
            $templateIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($projectInfo->column('project_template_id'));

            $projectId = array_values(array_unique(array_merge($projectId, $templateIds)));
        }
        $model = $model->whereIn('project_id', $projectId);

        return $model
            ->where($is_enable)
//            ->where([
//                'project_id' => $projectId,
////                'project_category_settings_type' => $this->projectCategorySettingsType,
//            ])
            ->where($where)
            ->with(['flow_status_text' => function ($sql) {
                $sql->field('flow_status_id, status_enum_id')
                    ->order('status_type ASC create_at DESC')
                    ->where(['is_delete' => BaseModel::DELETE_NOT])
                    ->with(['enumDetail' => function ($sql) {
                        $sql->bind(['status_enum_name' => 'name', 'colour']);
                    }, 'flowStatusDetail' => function ($sql) {
                        $sql->bind(['status_flow_name']);
                    }]);
            }])
            ->order('project_category_settings_type ASC, is_enable DESC, sort ASC, create_at DESC')
            ->field('project_category_settings_id, project_category_settings_type, category_name, category_en_name, icon, template_id, flow_status_id')
            ->hidden(['flow_status_id'])
            ->select()
            ->toArray();
    }

    /**
     * 根据类别id获取对应的 工作流id 及 状态库id
     * @param int $categorySettingsId
     * @return array
     * User Long
     * Date 2024/9/2
     */
    public static function getDefaultFlowStatusByCategorySettingsId(int $categorySettingsId): array
    {
        $flowStatusId = ProjectCategorySettingsModel::status()->where([
            'project_category_settings_id' => $categorySettingsId,
            'is_enable' => BaseModel::ENABLE_YES
        ])->value('flow_status_id');

        if (!$flowStatusId) {
            throw new ParamsException("类别不存在或已删除！");
        }

        $statusEnumId = FlowStatusTextLogic::getDefaultStatusEnumId($flowStatusId);

        return compact('flowStatusId', 'statusEnumId');
    }

    /**
     * 根据类别id获取 类别，流程 数据
     * @param int $categorySettingsId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/9
     */
    public static function findDataByCategorySettingsId(int $categorySettingsId)
    {
        $categorySettingsData = ProjectCategorySettingsModel::status()->where([
            'project_category_settings_id' => $categorySettingsId,
            'is_enable' => BaseModel::ENABLE_YES
        ])->find();

        if (!$categorySettingsData) {
            throw new ParamsException("类别不存在或已删除！");
        }

        $statusEnumData = FlowStatusTextLogic::getDefaultStatusByFlowStatusId($categorySettingsData->flow_status_id);

        return compact('categorySettingsData', 'statusEnumData');
    }

    /**
     * 根据类别id获取 类别数据
     * @param int $categorySettingsId
     * @param bool $fullQuery
     * @return ProjectCategorySettingsModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/14
     */
    public static function findCategorySettingsDataByCategorySettingsId(int $categorySettingsId, bool $fullQuery = false)
    {
        $model = ProjectCategorySettingsModel::where([
            'project_category_settings_id' => $categorySettingsId,
        ]);

        if (!$fullQuery) {
            $model = $model->where(['is_delete' => BaseModel::DELETE_NOT, 'is_enable' => BaseModel::ENABLE_YES]);
        }

        return $model->find();
    }

    /**
     * 根据工作流id 查询 类别数据
     * @param int $flowStatusId
     * @return ProjectCategorySettingsModel|array|mixed|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/9/4
     */
    public static function findCategorySettingByFlowStatusId(int $flowStatusId): mixed
    {
        return ProjectCategorySettingsModel::status()->where([
            'flow_status_id' => $flowStatusId
        ])->find();
    }

    /**
     * 获取相关需求id集合
     * @param int $categorySettingId
     * @throws ClientResponseException
     * @throws ServerResponseException
     * @return array
     * User Long
     * Date 2024/9/4
     */
    public function getRelevanceDemand(int $categorySettingId): array
    {
        return WorkItemsLogic::getItemByCategorySettingId($this->projectCategorySettingsType, $categorySettingId);
    }

    /**
     * 修改相关需求
     * @param array $workItemIds 工作项id合集
     * @param int $categorySettingsId 流转至的分类id
     * @throws ClientResponseException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws MissingParameterException
     * @throws ModelNotFoundException
     * @throws ServerResponseException
     * @throws Throwable
     * User Long
     * Date 2024/9/4
     */
    public function updateRelevanceDemand(array $workItemIds, int $categorySettingsId): void
    {
        $flowStatus = [];

        // 查询默认工作流信息
        if ($this->isDemand() || $this->isDefect()) {
            $model = ProjectCategorySettingsModel::findByCategoryId($this->projectCategorySettingsType, $categorySettingsId);
            if ($model) {
                $flowStatus['flow_status_id'] = $model->flow_status_id;
                $flowStatus['status_enum_id'] = FlowStatusTextLogic::getDefaultStatusEnumId($model->flow_status_id);
            }
        }

        (new WorkItemsLogic)->setItemsCategorySettingId($workItemIds, $categorySettingsId, $flowStatus);
    }

    /**
     * 需求是否更新工作流信息
     * @return bool
     * User Long
     * Date 2024/9/4
     */
    private function isDemand(): bool
    {
        return $this->projectCategorySettingsType == BaseModel::SETTING_TYPE_DEMAND;
    }

    /**
     * 缺陷是否更新工作流信息
     * @return bool
     * User Long
     * Date 2024/10/30
     */
    private function isDefect(): bool
    {
        return $this->projectCategorySettingsType == BaseModel::SETTING_TYPE_DEFECT;
    }

    /**
     * 复制类别
     * @param int $oldProjectId
     * @param int $newProjectId
     * @param array $templateData
     * @param array $flowProcessData
     * @param array $flowStatusData
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/10/14
     */
    public static function copyProjectDataByProjectId(int $oldProjectId, int $newProjectId, array $templateData, array $flowProcessData, array $flowStatusData)
    {
        $oldProjectData = ProjectCategorySettingsModel::status()
            ->where([
                'project_id' => $oldProjectId,
            ])->select();

        foreach ($oldProjectData as $oldProjectDatum) {
            // 复制类别
            $projectCategorySettingsModel = new ProjectCategorySettingsModel();
            $projectCategorySettingsModel->save([
                'attribution_id' => $oldProjectDatum->project_category_settings_id,
                'category_name' => $oldProjectDatum->category_name,
                'category_en_name' => $oldProjectDatum->category_en_name,
                'icon' => $oldProjectDatum->icon,
                'template_id' => $templateData[$oldProjectDatum->template_id] ?? $oldProjectDatum->template_id,
                'flow_status_id' => $flowStatusData[$oldProjectDatum->flow_status_id] ?? $oldProjectDatum->flow_status_id,
                'flow_process_id' => $flowProcessData[$oldProjectDatum->flow_process_id] ?? $oldProjectDatum->flow_process_id,
                'is_enable' => $oldProjectDatum->is_enable,
                'sort' => $oldProjectDatum->sort,
                'project_category_settings_type' => $oldProjectDatum->project_category_settings_type,
                'project_id' => $newProjectId
            ]);
        }
    }

    /**
     * 获取可用类别
     * @param int $projectId
     * @param int $categoryId
     * @return mixed
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2024/11/18
     */
    public function getAvailableCategoryIdByCategoryId(int $projectId, int $categoryId)
    {
        $model = ProjectCategorySettingsModel::findByAttributionId($this->projectCategorySettingsType, $categoryId, $projectId);

        // 当前类别不存在
        if (!$model || $model->is_enable != ProjectCategorySettingsModel::IS_ENABLE) {
            // 获取可用类别
            $model = $this->selector($projectId);

            if (!$model) {
                throw new NotFoundException();
            }

            // 获取第一个类别
            $model = reset($model);
        }

        return $model['project_category_settings_id'] ?? 0;
    }

    /**
     * 获取复制至项目/模板数据
     * @param int $categoryId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/11
     */
    public function getCopyToSelector(int $categoryId)
    {
        $model = ProjectCategorySettingsModel::findByCategoryId($this->projectCategorySettingsType, $categoryId);

        if (!$model) {
            throw new NotFoundException();
        }

        $res = [];

        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        if (!$projectInfo) {
            return $res;
        }

        // 根据是否模板，查询对应下啦数据
        switch ($projectInfo->is_template) {
            case ProjectModel::IS_TEMPLATE_YES:
                $res['data'] = (new ProjectTemplateLogic())->getTemplateSelector([
                    ['project_template_id', '!=', $projectInfo->project_template_id]
                ]);
                break;
            case ProjectModel::IS_TEMPLATE_NOT:
                $res['data'] = (new ProjectInfoLogic())->getProjectSelector([
                    ['project_id', '!=', $projectInfo->project_id],
//                    ['project_template_id', '=', $projectInfo->project_template_id]
                ]);
                break;
        }

        $res['is_template'] = $projectInfo->is_template;

        return $res;
    }

    /**
     * 数据复制至项目/模板
     * @param int $categoryId
     * @param array $copyToIds
     * @param int $isTemplate
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws Throwable
     * User Long
     * Date 2025/4/12
     */
    public function categoryCopyTo(int $categoryId, array $copyToIds, int $isTemplate)
    {
        $oldCategorySettings = ProjectCategorySettingsModel::status()
            ->with(['template', 'flowStatus', 'flowStatusText', 'flowProcess'])
            ->where(['project_category_settings_id' => $categoryId])
            ->find();

        if (!$oldCategorySettings) {
            throw new NotFoundException();
        }
        $oldCategorySettings = $oldCategorySettings->toArray();
        $oldProjectId = $oldCategorySettings['project_id'];

        $newProjectIds = [];
        $newName = null;
        switch ($isTemplate) {
            case ProjectModel::IS_TEMPLATE_YES:
                $newProjectIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($copyToIds);
                $newName = ProjectTemplateLogic::getTemplateNameByProjetIds($copyToIds);
                break;
            case ProjectModel::IS_TEMPLATE_NOT:
                $newProjectIds = $copyToIds;
                $newName = ProjectInfoLogic::getProjetNameByProjetIds($copyToIds);
                break;
        }

        $res = ['data' => [], 'is_rename' => false];
        $diffData = ProjectCategorySettingsModel::status()->whereIn('project_id', $newProjectIds)->select();

        try {
            DBTransaction::begin();
            foreach ($newProjectIds as $newProjectId) {
                // 重置数据
                $newCategorySettings = $oldCategorySettings;

                // 复制模板数据
                if ($newCategorySettings['template']) {
                    $templateData = $newCategorySettings['template'];

                    // 复制自定义字段数据
                    $newFieldConfigData = FieldConfigLogic::copyCustomizeFieldFromCategory(
                        $templateData['module_id'],
                        $oldProjectId,
                        $newProjectId,
                        $templateData['template_content']
                    );
                    $templateData['template_content'] = $newFieldConfigData['field_content'];

                    if (!$res['is_rename'] && $newFieldConfigData['is_rename']) {
                        $res['is_rename'] = true;
                    }

                    if (!empty($newFieldConfigData['data']['field_config'])) {
                        $res['data'][$newProjectId]['field_config'] = $newFieldConfigData['data']['field_config'];
                    }

                    // 保存并获取新的模板id
                    $newCategorySettingsData = TemplateLogic::copyTemplateByCategory($templateData, $newProjectId);
                    $newCategorySettings['template_id'] = $newCategorySettingsData['id'];

                    if (!$res['is_rename'] && $newCategorySettingsData['is_rename']) {
                        $res['is_rename'] = true;
                    }

                    if (!empty($newCategorySettingsData['data']['template_config'])) {
                        $res['data'][$newProjectId]['template_config'] = $newCategorySettingsData['data']['template_config'];
                    }
                }

                // 复制工作流数据
                if ($newCategorySettings['flowStatus']) {
                    $oldFlowStatusId = $newCategorySettings['flowStatus']['flow_status_id'];
                    $flowProcessIds = $flowProcessData = [];
                    if ($newCategorySettings['flowProcess']) {
                        $newFlowProcess = FlowProcessLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId, $newCategorySettings['flowProcess']['flow_process_id']);
                        $flowProcessIds = [$newFlowProcess['flow_process_id']];
                        $newCategorySettings['flow_process_id'] = $newFlowProcess['flow_process_id'];
                        $flowProcessData = [$newFlowProcess['attribution_id'] => $newFlowProcess['flow_process_id']];

                        if (!$res['is_rename'] && $newFlowProcess['is_rename']) {
                            $res['is_rename'] = true;
                        }

                        if (!empty($newFlowProcess['data']['flow_process'])) {
                            $res['data'][$newProjectId]['flow_process'] = $newFlowProcess['data']['flow_process'];
                        }
                    }

                    // 根据流程id查询 节点id（key 为归属来源id）
                    $flowProcessNodeData = FlowProcessLogic::selectAttributionIdByFlowProcessIds($flowProcessIds);

                    // 复制状态库
                    $statusEnumData = FlowStatusEnumLogic::selectAttributionIdByProjectId();

                    // 复制工作流
                    $newFlowStatus = FlowStatusLogic::copyProjectDataByProjectId($oldProjectId, $newProjectId, $flowProcessData, $statusEnumData, $oldFlowStatusId);
                    $oldFlowStatusIds = [$oldFlowStatusId];
                    $flowStatusIds = [$newFlowStatus['flow_status_id']];
                    $newCategorySettings['flow_status_id'] = $newFlowStatus['flow_status_id'];
                    $flowStatusData = [$newFlowStatus['attribution_id'] => $newFlowStatus['flow_status_id']];

                    if (!$res['is_rename'] && $newFlowStatus['is_rename']) {
                        $res['is_rename'] = true;
                    }
                    if (!empty($newFlowStatus['data']['status_flow'])) {
                        $res['data'][$newProjectId]['status_flow'] = $newFlowStatus['data']['status_flow'];
                    }
                    $flowStatusTextData = FlowStatusTextLogic::selectAttributionIdByFlowStatusIds($flowStatusIds);

                    // 复制状态与节点关系
                    FlowStatusNodeRelationLogic::copyProjectDataByOldFlowStatusId($oldFlowStatusIds, $flowStatusData, $flowStatusTextData, $flowProcessNodeData);

                    // 复制状态流转
                    FlowStatusTransferLogic::copyProjectDataByProjectId($oldFlowStatusIds, $flowStatusData, $flowStatusTextData, $statusEnumData);

                    // 复制工作流程节点关联关系
                    FlowProcessNodeRelationLogic::copyProjectDataByOldNodeId($flowProcessNodeData);
                }

                $rename = $newCategorySettings['category_name'];
                $renameEN = $newCategorySettings['category_en_name'];

                $isName = null;
                if (!$diffData->where('project_id', $newProjectId)->where('category_name', $rename)->isEmpty()) {
                    if (!$res['is_rename']) {
                        $res['is_rename'] = true;
                    }
                    $isName = true;
                    $rename = ProjectCategorySettingsModel::generateName($rename, $newProjectId);
                }
                if (!$diffData->where('project_id', $newProjectId)->where('category_en_name', $renameEN)->isEmpty()) {
                    if (!$res['is_rename']) {
                        $res['is_rename'] = true;
                    }
                    $isName = true;
                    $renameEN = ProjectCategorySettingsModel::generateEnName($renameEN, $newProjectId);
                }
                // 复制类别
                $projectCategorySettingsModel = new ProjectCategorySettingsModel();
                $projectCategorySettingsModel->save([
                    'attribution_id' => $newCategorySettings['project_category_settings_id'],
                    'category_name' => $rename,
                    'category_en_name' => $renameEN,
                    'icon' => $newCategorySettings['icon'],
                    'template_id' => $newCategorySettings['template_id'],
                    'flow_status_id' => $newCategorySettings['flow_status_id'],
                    'flow_process_id' => $newCategorySettings['flow_process_id'],
                    'is_enable' => ProjectCategorySettingsModel::IS_CLOSE,
                    'sort' => $newCategorySettings['sort'],
                    'project_category_settings_type' => $newCategorySettings['project_category_settings_type'],
                    'project_id' => $newProjectId
                ]);

                if ($isName) {
                    $res['data'][$newProjectId]['category'][] = [
                        'category_id' => $projectCategorySettingsModel->project_category_settings_id,
                        'category_name' => $rename,
                        'category_en_name' => $renameEN,
                        'project_id' => $newProjectId,
                        'url' => self::RENAME_URL
                    ];

                }

                if (!empty($res['data'][$newProjectId])) {
                    $res['data'][$newProjectId]['project_name'] = $newName[$newProjectId] ?? '';
                }
            }

            DBTransaction::commit();
        } catch (Throwable $e) {
            DBTransaction::rollback();
            throw $e;
        }

        $res['data'] = array_values($res['data']);

        return $res;
    }

    /**
     * 更新类别名称
     * @param int $categoryId
     * @param string $categoryName
     * @param string $categoryEnName
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public function rename(int $categoryId, string $categoryName = '', string $categoryEnName = '')
    {
        $model = ProjectCategorySettingsModel::findByCategoryId($this->projectCategorySettingsType, $categoryId);
        if ( ! $model) {
            throw new NotFoundException();
        }

        $projectInfo = ProjectInfoLogic::getProjectData($model->project_id);
        $name = match ($projectInfo->is_template) {
            ProjectTemplateModel::IS_TEMPLATE_YES => ProjectTemplateLogic::getProductNameByProductId($projectInfo->project_template_id),
            ProjectTemplateModel::IS_TEMPLATE_NO => '【'.$projectInfo->project_name.'】',
            default => '',
        };

        $categoryNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndName($model->project_id, $this->projectCategorySettingsType, $categoryName);
        if ($categoryNameExist && $categoryNameExist->project_category_settings_id != $categoryId) {
            throw new ParamsException($name.'-分类名称已存在');
        }

        $categoryEnNameExist = ProjectCategorySettingsModel::findCategoryByProjectAndEnName($model->project_id, $this->projectCategorySettingsType, $categoryEnName);
        if ($categoryEnNameExist && $categoryEnNameExist->project_category_settings_id != $categoryId) {
            throw new ParamsException($name.'-图标设置名称已存在');
        }

        // 校验名称是否与模板相同
        $this->validateProjectTemplateData($projectInfo, $categoryName, $categoryEnName);

        if ($categoryName) $model->category_name = $categoryName;
        if ($categoryEnName) $model->category_en_name = $categoryEnName;

        $model->save();
    }
}
