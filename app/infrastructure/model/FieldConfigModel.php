<?php
declare (strict_types=1);

namespace app\infrastructure\model;

use app\project\logic\ProjectInfoLogic;
use app\project\logic\ProjectTemplateLogic;
use app\project\model\ProjectModel;
use basic\BaseModel;
use exception\ParamsException;
use think\facade\Cache;
use think\model\relation\HasOne;
use traits\CreateAndUpdateModelTrait;
use traits\OperationLogTrait;


/**
 * This is the model class for table "t_field_config"
 * @property int    $field_id        id
 * @property int    $create_by       创建人
 * @property string $create_by_name  创建人名称
 * @property string $create_at       创建时间
 * @property string $is_delete       是否删除;1-是 0-否
 * @property int    $update_by       更新人
 * @property string $update_by_name  更新人名称
 * @property string $update_at       更新时间
 * @property string $field_type      字段类型;1-系统 2-自定义
 * @property string $remark          备注
 * @property string $field_label     字段显示名称
 * @property string $field_name      字段名
 * @property string $field_component 组件内容
 * @property int    $field_sort      排序值 降序排序
 * @property string $module_id       归属模块;1-终端 2微服务 3-迭代
 * @property string $project_id      所属项目id，属于项目的模块：4、5、6、7、8、9，只有自定义字段有所属项目这个概念，系统字段为通用，所有项目都有
 * @property string $allow_setting   是否允许配置;1-允许 0-不允许
 */
class FieldConfigModel extends BaseModel
{
    //操作日志
    use OperationLogTrait;
    use CreateAndUpdateModelTrait;

    const  FIELD_TYPE_SYSTEM = 1;   //字段类型-系统
    const  FIELD_TYPE_CUSTOMIZE = 2;   //字段类型-自定义
    const  ALLOW_SETTING_YES = 1;   //是否允许配置;1-允许
    const  ALLOW_SETTING_NO = 0;   //是否允许配置;0-不允许


    const TEMPLATE_DEFAULT_YES = 1;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //是否是模版固定字段;1-是
    const TEMPLATE_DEFAULT_NO = 0;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          //是否是模版固定字段;0-否

    //所属模块
    const MODULE_TYPE_CLIENT = 1;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      //终端
    const MODULE_TYPE_MICROSERVICE = 2;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  //微服务
    const MODULE_TYPE_PRODUCT = 3;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             //产品
    const MODULE_TYPE_ITERATION = 4;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     //迭代
    const MODULE_TYPE_REQUIREMENT = 5;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   //需求
    const MODULE_TYPE_TASK = 6;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      //任务
    const MODULE_TYPE_DEFECT = 7;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     //缺陷
    const MODULE_TYPE_TEST_CASE = 8;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //测试用例
    const MODULE_TYPE_TEST_PLAN = 9;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    //测试计划
    const MODULE_TYPE_TEST_CASE_PLAN = 10;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //测试计划 规划与执行


    //需要项目id的模块
    const REQUIRE_PROJECT_MODULE
        = [
            self::MODULE_TYPE_ITERATION,
            self::MODULE_TYPE_REQUIREMENT,
            self::MODULE_TYPE_TASK,
            self::MODULE_TYPE_DEFECT,
            self::MODULE_TYPE_TEST_CASE,
            self::MODULE_TYPE_TEST_PLAN,
        ];


    const PROJECT_NOT_HAS = 0;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     //不属于任何项目


    //只有属于REQUIRE_PROJECT_MODULE中的，并且是系统字段才会有分类
    const CATEGORY_BASE = 1;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  //分类id，1-基础字段
    const CATEGORY_USER_DATE = 2;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //分类id，2-人员与时间字段
    const CATEGORY_WORK_HOURS = 3;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       //分类id，3-工时字段


    const COMPONENT_TYPE_EDITOR = 'Editor';//富文本类型组件


    /**
     * 自定义字段名缓存Key
     */
    const CACHE_FIELD_NAME_KEY = "filed_name:%s";

    const LIST_FIELDS
        = [
            'field_id',
            'field_label',
            'field_name',
            'field_component',
            'remark',
            'field_sort',
            'field_type',
            'template_default',
            'category_id',
            //        'displayDefault',
            'allow_setting',
            'is_edit',
            'module_id',
            'component_type',
        ];


    protected $name = 'field_config';
    protected $pk = 'field_id';
    protected array $logFieldList
        = [
            'field_label' => '字段名称',
            'remark'      => '备注',
            //            'field_component'=>'组件',
            'field_sort'  => '排序',
            //            'create_by'      => '创建人',
            //            'create_by_name' => '创建人名称',
            //            'create_at'      => '创建时间',
            //            'update_by'      => '更新人',
            //            'update_by_name' => '更新人名称',
            //            'update_at'      => '更新时间',
        ];

    /**
     * 新增/修改需要记录日志的字段，字段名=>展示名称
     * @param $fieldId
     * @return FieldConfigModel|array|mixed|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/7/8 上午10:10
     */
    public static function findById($fieldId)
    {
        return static::where(['field_id' => $fieldId, 'is_delete' => self::DELETE_NOT])->find();
    }

    /**
     * 获取下一个字段名称
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getNextFieldName(): string
    {
        $maxModel = static::where([
            'field_type' => self::FIELD_TYPE_CUSTOMIZE,
        ])
            ->order('field_id', 'desc')->find();
        if ( ! $maxModel) {
            return 'f1';
        }

        return 'f'.(((int)ltrim($maxModel->field_name, 'f')) + 1);
    }

    /**
     * 获取指定模块的指定字段
     * @param $moduleId
     * @param $fieldLabel
     * @return FieldConfigModel|array|mixed|\think\Model|null
     */
    public static function findModuleFieldLabel($moduleId, $fieldLabel, $projectId)
    {
        $where = [
            ['is_delete', '=', BaseModel::DELETE_NOT],
            ['field_label', '=', $fieldLabel],
            ['module_id', '=', $moduleId],
        ];
        if (self::isRequireProjectId($moduleId)) {
            $where[] = ['project_id', 'in', [$projectId, self::PROJECT_NOT_HAS]];
        }

        return static::where($where)->find();
    }

    /**
     * 获取指定模块的所有组件
     * @param $moduleId
     * @param $projectId
     * @return FieldConfigModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/12/10 上午10:19
     */
    public static function findListByModuleIdAndProjectId($moduleId, $projectId)
    {
        $where = [
            'is_delete' => BaseModel::DELETE_NOT,
            'module_id' => $moduleId,
        ];
        if (self::isRequireProjectId($moduleId)) {
            $where['project_id'] = [$projectId, self::PROJECT_NOT_HAS];
        }

        return static::where($where)->select();
    }

    /**
     * 删除字段
     * @return void
     */
    public function deleteField(): void
    {
        if ($this->field_type == FieldConfigModel::FIELD_TYPE_SYSTEM) {
            throw new ParamsException('系统字段不可删除！');
        }
        $this->is_delete = BaseModel::DELETE_YES;
    }

    public function getFieldComponentAttr($value)
    {
        return json_decode($value, true);
    }

    public function setFieldComponentAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    /**
     * @return string[]
     */
    public function getLogFieldList(): array
    {
        return $this->logFieldList;
    }


    public function toDetail()
    {
        return $this->visible(self::LIST_FIELDS);
    }


    public static function getLabelByFieldName($fieldName)
    {
        $cacheKey = sprintf(self::CACHE_FIELD_NAME_KEY, $fieldName);
        $fieldLabel = Cache::get($cacheKey);

        if ($fieldLabel) {
            return json_decode($fieldLabel, true);
        }
        $model = static::where("field_name", $fieldName)->field("field_label,field_name,field_component")->find();
        if ( ! $model) {
            return null;
        }

        Cache::set($cacheKey, $model->toJson(), 3600 * 72);

        return $model->toArray();
    }

    public function deleteCache()
    {
        Cache::delete(sprintf(self::CACHE_FIELD_NAME_KEY, $this->field_name));
    }


    /**
     * 根据fieldName获取
     * @param  array|null  $fieldNameList  null返回所有
     * @param              $moduleId
     * @param              $projectId
     * @return FieldConfigModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date   2024/8/31 17:28
     */
    public static function getListByFieldName(array|null $fieldNameList, $moduleId, $projectId = null)
    {
        $where = [
            'is_delete' => BaseModel::DELETE_NOT,
            'module_id' => $moduleId,
        ];
        if ($fieldNameList) {
            $where['field_name'] = $fieldNameList;
        }

        $whereOr = [
            ['project_id', '=', FieldConfigModel::PROJECT_NOT_HAS],
        ];

        if ($projectId) {
            $whereOr[] = ['project_id', '=', $projectId];
            // 获取模板项目
            $projectInfo = ProjectInfoLogic::getProjectDataSet($projectId)->where('is_template', 0);
            if ( ! $projectInfo->isEmpty()) {
                $templateIds = ProjectTemplateLogic::getProjectIdsByProjectTemplateIds($projectInfo->column('project_template_id'));
                $whereOr[] = ['project_id', 'in', $templateIds];
            }

        }

        return self::where($where)
            ->where(function ($query) use ($whereOr) {
                $query->whereOr($whereOr);
            })
            ->field(FieldConfigModel::LIST_FIELDS)
            ->order('field_sort desc')
            ->select();
    }

    /**
     * 判断模块是否含有所属项目
     * @param $moduleId
     * @return bool
     * <AUTHOR>
     * @date   2024/8/22 17:26
     */
    public static function isRequireProjectId($moduleId)
    {
        return in_array($moduleId, self::REQUIRE_PROJECT_MODULE);
    }

    /**
     * 根据 模块id 字段名  获取组件内容
     * @param  int    $moduleId
     * @param  array  $fieldName
     * @return array
     * User Long
     * Date 2024/11/7
     */
    public static function columnFieldComponentByFieldName(int $moduleId, array $fieldName = [])
    {
        return self::status()->where('module_id', $moduleId)->whereIn('field_name', $fieldName)->column('field_id, field_label, field_name, field_component');
    }

    /**
     * 生成复制名称
     * @param $name
     * @param $projectId
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * User Long
     * Date 2025/4/14
     */
    public static function generateName($name, $projectId): string
    {
        $query = static::status()->where(['project_id' => $projectId]);

        return generateCopyName($name, 'field_label', $query);
    }

    /**
     * 根据 字段名称 获取当前项目下的指定字段
     * @param  int     $projectId
     * @param  string  $fieldLabel
     * @return mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function findFieldAndName(int $projectId, string $fieldLabel): mixed
    {
        return static::status()->where([
            'project_id'  => $projectId,
            'field_label' => $fieldLabel
        ])->find();
    }

    /**
     * 预加载 - 项目表
     * @return HasOne
     */
    public function project(): HasOne
    {
        return $this->hasOne(ProjectModel::class, 'project_id', 'project_id');
    }


    /**
     * 获取项目配置的字段
     * @param  int  $projectId
     * @param  int  $moduleId
     * @return array
     * @exception Exception
     * <AUTHOR>
     * @date      2025/5/16 10:18
     */
    public static function getLabelNameFormatKv(int $projectId, int $moduleId = 0)
    {
        return self::status()
            ->whereIn('project_id', [0, $projectId])
            ->where('module_id', $moduleId)
            ->column('field_label', 'field_name');
    }

    public static function getColumnFieldNameFormat(int $projectId, int $moduleId = 0)
    {
        return self::status()
            ->whereIn('project_id', [0, $projectId])
            ->where('module_id', $moduleId)
            ->column('field_label,component_type', 'field_name');
    }
}
